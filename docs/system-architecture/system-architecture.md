# Fridge Inventory System Architecture

## Overview
The system consists of two applications running on a device (PC, Laptop, or Raspberry Pi) with a connected camera:

1. **Python Script (AI Items Tracker)**
    - Continuously detects items put in and taken out of the fridge.
    - Saves data in the filesystem:
        - Log of all item movements.
        - Latest fridge content state with a timestamp.

2. **Node.js Local Server**
    - Serves a local Vue.js web application.
    - Accessible on the local device and within the home Wi-Fi network.
    - Allows to view fridge content and movements history.

## Pros
- **For team:** Simplifies implementation, allowing focus on model training.
- **For users:** Ensures data privacy by storing everything locally.
- **For companies:** Provides a flexibility of choice for future expansion (client-server infrastructure, integration with existing smart fridge systems).

## Cons
- **For users:** Lacks online backup and remote access outside the local network.

## App Design
https://www.figma.com/design/OTe6y92U1XQ28zCr2v4VJi/Fridge-Inventory-Design?node-id=0-1&t=akGJQnLyflTlucy3-1

![Desktop.png](Desktop.png)

![Mobile.png](Mobile.png)