# Problems and Solutions Log

This document captures the challenges we faced during the project and the solutions we discovered

---

## 🧠 **1. Pretrained Models Limitations**

**Problem:** Pretrained models we tested weren’t accurate enough for recognizing grocery items in our setup.

**Solution:** Captured a custom dataset and labeled the data.

---

## 🎥 **2. Motion Blur in Webcam Video**

**Problem:** When capturing video of items being placed in or removed from the fridge, moving objects appeared too blurred, making it impossible to train or test our model effectively.

**Solution:** Lowered the camera exposure time to reduce motion blur.

---

## 🔌 **3. Long Cable Bandwidth**  

**Problem:** Since the camera and the improvised fridge are about several meters away from the computer, we need a long enough cable to connect them.

**Failed Solution:** USB extension cable or the USB-Ethernet-USB configuration with adapters reduce the bandwidth of the video signal, lowering the video quality.