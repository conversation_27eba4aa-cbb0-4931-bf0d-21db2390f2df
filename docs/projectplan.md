# Project plan
Due date: August 2025: 8 Months

## Epics

- training data (3 months)
  - finding existing datasets
  - determine classes and number of classes
  - own dataset
    - record data
    - label data
  - prepare dataset
    - augmentation
    - synthesize data?

- model for object detection (1 month)
  - find pretrained models
  - train yolo model
  - finetune model
  - evaluation

- action classification (transform stream of bounding boxes into discrete insertions or removals of food) (1-2 weeks)
  - at the start without sensor and manual cutting sequence to action
  - convert list of bounding boxes of sequence to action (in/out)

- inventory tracking (1 week)
  - persist the action history into a database

- GUI (2 months)
  - show the current contents
  - show the action history

- optional: sensor to detect opening and closing of door (1 month)

- write documentation (1 month)
  - pipeline diagram
  - technical documentation
  - individual contribution

- camera solution without usb cable
  - model on raspberry pi?

## Milestones

- MVP (2025-02-11)
  - working physical setup (camera connected to PC)
    - [x] camera setup
    - [x] USB cable
  - [x] object detection (very limited, 2 classes)
  - [x] action with class, print to console
  - [x] test data
    - [x] recordings for actions with our 2 classes (apple, banana)
  - [x] concept for database and GUI

- improve object detection & setup database (2025-03-25)
  - [x] improve object tracking
    - [x] test current model
    - [x] explore tracking model
  - [x] persist actions into database
  - [x] first version of user interface
  - improve training data
    - [x] choose classes
    - [x] create own training data
    - [x] fix mislabeled data
  - raspberry pi
    - [x] set up OS and environment
    - [x] convert ipynb to python scripts
  - [x] improve tracking robustness

- more tuning / comparison of models, graphical interface (2025-05-19)
  - [x] augment with generated data
  - ~~[ ] research pretrained GANs~~
  - ~~[ ] research generative models~~
  - ~~[ ] check limits of DALL-E etc~~
  - [x] test limits of approach
    - [x] cannot detect unknown brands
    - [x] open question: unclear how to deal with multiple objects
  - [x] deploy on raspberry pi
    - [x] cannot currently run on cpu
    - [x] convert model to HEF
    - [x] deploy hailo pipeline
  - [x] benchmark (F1 score, precision, recall)
  - [x] cost analysis (local vs cloud) + performance
    - [x] costs
    - ~~[ ] performance~~
  - [x] test run in lab

- deployment, testing, multiple objects (2025-06-02)
  - [x] record data with false positive objects
  - [x] record new validation data 
  - [x] label new validation data
  - [x] get hailo runtime running on raspberry
  - [ ] run hailo model (inference) on raspberry
  - [ ] sensible services on raspberry
  - [x] ~~test cloud performance again~~
  - [ ] train model to detect multiple objects
  - [ ] end to end test with food items

- future ideas (outlook)
  - check public zero shot object detectors (YOLO world?)
  - pipeline to train model on few shot data using synthetic data

- project finalization and documentation (2025-06-16)
  - [x] benchmarks for each model 
  - [ ] cleaned up benchmarks differentiating seen brands and unseen brands
  - [ ] 20 pages for team
  - [ ] 4-5 pages individual docs
  - [ ] complete README
  - [ ] final presentation (what is required?)
