%!TEX root=Masterdatei.tex
%MAIN-DOCUMENT
%INCLUDIERT ALLER FILES DER ORDNERSTRUKTUR

% Header beinhaltet Dokumentenklasse sowie includierte Packages
\input{Header/Header}

%fertigen Kopfzeilenstil aktivieren
\makroFH-Kopfzeilenstil

%Zeilenabstand * 1.25 (default)
\renewcommand{\baselinestretch}{1.25}\normalsize
%(Kommentar entfernen, um Zeilenabstand
% auf 1,5-fache Groesse zu ueb<PERSON>chreiben)
%\renewcommand{\baselinestretch}{1.50}\normalsize

%Startpunkt jedes LaTex-Dokuments
\begin{document}

%<--- <PERSON><PERSON>e Glossar
%\input{Glossar/Glossar}

%Benutze Glossar --->

%Verwende Muster-Titelseite 
\include{Titelseite/titelseite}
%Füge leere Seite ein (optional)
%\input{leereSeite}
%Verwende Muster-Erklärung zur selbstständigen Arbeit 
%\include{Erklaerung/Erklaerung}
%Füge leere Seite ein (optional)
%\input{leereSeite}
%Verwende Muster-Abstract 
\input{Abstract/Abstract}


%Liste Menüpunkte als Inhaltsverzeichnis
\tableofcontents
\setcounter{page}{1}

%Jedes Kapitel in eigener Ordnerstruktur 
%1.Kapitel ist die Zusammenfassung
\input{K2_Aufgabenstellung/K2_Text}
\input{K3_Team/K3_Text}
\input{K4_Tools/K4_Text}
\input{K5_Verlauf/K5_Text}
\input{K6_Ergebnis/K6_Text}
\input{K7_Fazit/K7_Text}

\appendix
\input{K8_IndvDoku/K8_1_Doku_Forster.tex}
\input{K8_IndvDoku/K8_1_Doku_Oleksenko.tex}
\input{K8_IndvDoku/K8_1_Doku_Schaller.tex}

% <--- BibTex mit Stil alpha
\bibliography{Bibliothek}{}
\bibliographystyle{unsrt}
% BibTex mit Stil alpha --->
 
% Abbildungsverzeichnis, Tabellenverzeichnis und Glossar ausgeben
\listoffigures
\listoftables
\nocite{*}
%\glsaddall % move from top to remove empty page in beginning
%\printglossaries 
\lstlistoflistings


%Ende jedes LaTex-Dokuments
\end{document}

