\chapter{\mbox{Individualdokumentation - Forster}}

\section{Datenaufnahme} (erste mal gemeinsam aufnehmen/skript schreiben (e.g. beleuchtungsparameter, etc))

\begin{itemize}
    \item aufnehmen mit skript \verb|record.py|
    \item Zuschneiden auf sichtbare Items mit losslesscut
    \item \verb|ffmpeg -i rec_sf/cut/output_yogurt.mp4 img_data/yogurt/frame_s_%04d.png|
\end{itemize}

\section{Labeling}
\subsection{Aufsetzen Labeltool}
Auswahl des Tools \verb|label-studio|, weil kostenlos, local, open-source. 
Alternativen waren XY (online), XY (kostenpflichtig) oder XY. 

Ausführen über Docker: 
1. container für bereitstellung der webui
1. container für bereitstellung der modelle

Bei der konfiguration des labeltools konnten die klassen festgelegt werden, sowie das Model für die vorhersage und zugehöriger threshold gesetzt werden.
\begin{lstlisting}[language=xml, caption={Auszug label-studio/label-studio.conf}]
    <View>
    <Image name="image" value="$image"/>
    <RectangleLabels name="label" toName="image" model_path="yolo-all200pcs.pt" model_score_threshold="0.7">
        
    <Label value="bacon" background="#FFA39E" predicted_values="bacon"/>
    <Label value="carrot" background="#D4380D" predicted_values="carrot"/>
    <Label value="cucumber" background="#FFC069" predicted_values="cucumber"/>
    <Label value="egg" background="#AD8B00" predicted_values="egg" />
    <Label value="eggs" background="#D3F261" predicted_values="eggs"/>
    <Label value="ketchup" background="#389E0D" predicted_values="ketchup"/>
    <Label value="mayonnaise" background="#5CDBD3" predicted_values="mayonnaise"/>
    <Label value="mustard" background="#096DD9" predicted_values="mustard"/>
    <Label value="yogurt" background="#ADC6FF" predicted_values="yogurt"/>
    </RectangleLabels>
    </View>
\end{lstlisting}

Connect model: http://localhost:9090
Importieren der Daten über Label tool cloud storage -> local files and import all .*png files from the mounted directory. (später identifikation der richtigen klassen über ordnerstruktur der bilder)
Export der Label in Json; convertieren in yolo format mit \verb|script|

\section{Model Training}
Aufbauen auf yolo v11
export der gelabelten daten und convertieren in yolo dataset \verb|train.ipynb|
train test split von 80/20
vermeiden von overfitting: early stopping

\textbf{tensorboard}
\begin{itemize}
    \item train4 manual labeled data with errors
    \item train5 manual labeled data without errors
    \item train6 synthetic data
    \item train7 synthetic data with occlusion
    \item train9 mix synthetic data with real data (30-70)
\end{itemize}

evals are run on a completly different dataset (not used for training, partly different brands meaning different colors, etc.)

Detection challenges:
\begin{itemize}
    \item blurry
    \item (partly) obscured
    \item reflective (foil, glass)
\end{itemize}

\section{Validation Data Set}
komplett neue aufnahmen (mit teilweise neuen marken -> neues aussehen)

\textbf{Erkenntnisse}
\begin{itemize}
    \item bisheriges lernen aufgrund von farben (bacon oft mit hand/ei vertauscht); roter senfdeckel = ketchup; grüner Ketchup-deckel = gurke
\end{itemize}

\textbf{synthetic data}
input images: crop and remove background: gimp

learnings
auch eval auf gleichen marken zeigt unsicherheit bei marken die unterschiedliche prod

yolo falscher ansatz? zu viel verdeckt?

Main problem: erkennt die objekte gar nicht sondern als hintergrund (objekte werden nicht als falsche klasse erkannt sondern gar nicht)

\begin{itemize}
    \item SIZE
    \item ROTATION
    \item OCCLUSION
    \item BRIGHTNESS
    \item GRAYSCALE
    \item BLUR
    \item HUE
\end{itemize}

\subsection{Evaluation}
Um aus den trainierten Modellen mit den unterschiedlichen Ansätzen das objektiv beste Modell zu finden, wurde eine Evaluation durchgeführt. Diese unterschied dabei zwischen bekannten und unbekannten Marken, da die neuen Marken nicht im Training enthalten waren und somit eine höhere Transferleistung erforderten. Mit den bekannten Marken konnte so ermittelt werden, wie gut das Modell die im Training vorhandenen Marken erkennt, und mit den unbekannten Marken konnte ermittelt werden, wie gut das Modell auf Marken mit anderem Aussehen generalisiert.

Als Vergleichswert wurde der F1-Score herangezogen. Dieser stellt den harmischen Mittelwert aus Precision und Recall dar und ist somit ein guter Indikator für die Qualität des Modells. Ein hoher Wert bedeutet, dass das Modell sowohl eine hohe Präzision (hoher Anteil von korrekten Positiv-Vorhersagen unter allen Positiv-Vorhersagen) als auch eine hohe Vollständigkeit (wenige False Negatives im Verhältnis aller Positiven) aufweist.
\begin{equation*}
    \text{Precision} = \frac{TP}{TP + FP} \qquad
    \text{Recall} = \frac{TP}{TP + FN} \qquad
    F_1 = 2 \cdot \frac{\text{Precision} \cdot \text{Recall}}{\text{Precision} + \text{Recall}}
\end{equation*}

\begin{figure}[H]
    \begin{minipage}{0.49\textwidth}
        \centering
        \includegraphics[width=1\textwidth]{K8_IndvDoku/f1s_same_brand.png}
        \caption{Bekannte Marken}
    \end{minipage}\hfill
    \begin{minipage}{0.49\textwidth}
        \centering
        \includegraphics[width=1\textwidth]{K8_IndvDoku/f1s_new_brand.png}
        \caption{Unbekannte Marken}
    \end{minipage}
\end{figure}

Man kann bei allen Modellen einen deutlichen Leistungsabfall bei den unbekannten Marken erkennen. Ein möglicher Grund hierfür könnte sein, dass das Modell die Marken nicht nur anhand der Form, sondern auch anhand der Farben gelernt hat. Die Farben unterscheiden sich aber für das gleiche Produkt je nach Marke stark, was zu einer schlechteren Generalisierung führt.

Bei genauerer Analyse des besten Modells zeigen sich detailiertere Unterschiede zwischen den Objekten.
\begin{figure}[H]
    \begin{minipage}{0.49\textwidth}
        \centering
        \includegraphics[width=1\textwidth]{K8_IndvDoku/f1_aug_long_same.png}
        \caption{Bekannte Marken}
    \end{minipage}\hfill
    \begin{minipage}{0.49\textwidth}
        \centering
        \includegraphics[width=1\textwidth]{K8_IndvDoku/f1_aug_long_new.png}
        \caption{Unbekannte Marken}
    \end{minipage}
\end{figure}

Auffällig: Senf sehr sehr schlecht (Deckel andere farbe als im Trainingsset, gelber Teil meist von Hand verdeckt); wieso carrot so schlecht -> eigentlich eindeutige Farbe? vermutlich zu wenig sichtbarer Teil (für einen Finger gehalten?) Weil auch eine eindeutige Farbe blau Jogurt sehr gut erkannt; bei jogurt ist aber immer der Deckel sichtbar. jogurt beste weil durch Form des objekts immer gewährleistet ist, dass der deckel voll sichtbar ist und eindeutige Farbe hat. 

\section{Mögliche Verbesserungen}
\begin{itemize}
    \item mehr Trainingsdaten (mehr Marken, mehr Bilder)
    \item größeres Yolo Model trainieren (bisher yolo v11n)
    \item Mehrere Kameraperspektiven kombinieren
    \item Nicht auf dem Weg in den Fridge sondern im Fridge erkennen (-> keine verdeckung durch Hand)
\end{itemize}