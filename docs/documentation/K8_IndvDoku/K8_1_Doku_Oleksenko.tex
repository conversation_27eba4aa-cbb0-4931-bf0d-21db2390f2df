\chapter{Individualdokumentation - <PERSON><PERSON><PERSON>}

\section{Datenbanksystem}

Wir haben eine SQLite-Datenbank implementiert, um sämtliche detektierten Objektbewegungen zu dokumentieren und den aktuellen Zustand des Kühlschranks zu speichern.
Das Datenbanksystem dient als gemeinsamer Speicher für das Python-Objekterkennungssystem und die JavaScript-Weboberflächen-App.

\subsection{Architektur und verwendete Technologien}

Die Daten werden in einer relationalen SQLite-Datenbank gespeichert, die lokal betrieben wird und keine externen Abhängigkeiten erfordert.
Dadurch wird eine einfache und ressourcenschonende Integration in die vorhandene Systemarchitektur gewährleistet.

Die Datenbank besteht aus zwei Tabellen:

\subsubsection*{1. \texttt{actions} – Historie aller Objektinteraktionen}

Diese Tabelle protokolliert jede erkannte Aktion (Einlegen oder Entnehmen eines Objekts) mit Zeitstempel und Objektklasse.
Die Datenstruktur ist wie folgt definiert:

\begin{center}
\begin{tabular}{@{}lll@{}}
\toprule
\textbf{Spalte} & \textbf{Typ} & \textbf{Beschreibung} \\ \midrule
\texttt{id} & INTEGER & Primärschlüssel, automatisch inkrementiert \\
\texttt{timestamp} & TEXT & Zeitpunkt der Aktion (ISO-Format) \\
\texttt{action\_type} & TEXT & ``in'' für Einlegen, ``out'' für Entnehmen \\
\texttt{item\_class} & TEXT & Klassifizierung des Objekts (z.\,B. ``milk'') \\
\bottomrule
\end{tabular}
\end{center}

\subsubsection*{2. \texttt{fridge\_inventory} – Aktueller Kühlschrankinhalt}

Diese Tabelle stellt den aktuellen Stand der im Kühlschrank befindlichen Objekte dar:

\begin{center}
\begin{tabular}{@{}lll@{}}
\toprule
\textbf{Spalte} & \textbf{Typ} & \textbf{Beschreibung} \\ \midrule
\texttt{item\_class} & TEXT & Objektklasse (Primärschlüssel) \\
\texttt{quantity} & INTEGER & Aktuelle Anzahl im Kühlschrank (auch negativ) \\
\bottomrule
\end{tabular}
\end{center}

Die Verwaltung erfolgt über ein Python-Modul (\texttt{db\_manager.py}), das alle Schreiboperationen kapselt.
Die Objekterkennungskomponente verwendet dieses Modul, um nach erfolgreicher Klassifikation und Aktionsdetektion den jeweiligen Eintrag in der \texttt{actions}-Tabelle zu erzeugen sowie die \texttt{fridge\_inventory}-Tabelle konsistent zu aktualisieren.

\subsection{Datenintegrität und Synchronisation}

Zur Vermeidung inkonsistenter Zustände wurden atomare Transaktionen in SQLite verwendet.
Die Weboberfläche hat ausschließlich Lesezugriff und ruft die Daten über definierte REST-API-Endpunkte des lokalen Servers ab.
Dies stellt sicher, dass visuelle Datenanzeigen stets den tatsächlichen Zustand repräsentieren.

\section{Weboberfläche zur Visualisierung und Benutzerinteraktion}

Zur benutzerfreundlichen Darstellung des Kühlschrankzustands und der protokollierten Aktionen wurde eine moderne Webanwendung entwickelt.
Diese folgt dem Client-Server-Paradigma und ist modular aufgebaut.

\subsection{Systemarchitektur}

Die Webanwendung gliedert sich in folgende Hauptkomponenten:

\begin{itemize}[topsep=0pt]
    \item \textbf{Frontend:} Realisiert mit Vue.js, zuständig für Darstellung und Benutzerinteraktion.
    \item \textbf{Backend:} Implementiert mit Node.js (TypeScript), dient als API-Gateway und hostet statische Frontend-Dateien.
    \item \textbf{Websocket-Kommunikation:} Echtzeitdatenübertragung zwischen Backend und Frontend bei neuen Aktionen.
\end{itemize}

Das Backend bietet folgende REST-Endpunkte:

\begin{itemize}[topsep=0pt]
    \item \texttt{GET /fridge-inventory}: Liefert aktuelle Inhalte des Kühlschranks.
    \item \texttt{GET /actions}: Gibt eine paginierte Liste der Aktionen zurück.
    \item \texttt{GET /actions-report}: Aggregierte Statistik der letzten 30 Tage nach Objektklasse.
\end{itemize}

\subsection{Benutzeroberfläche}

Die Benutzeroberfläche besteht aus drei Hauptbereichen:

\begin{enumerate}[label=\alph*)]
    \item \textbf{Aktueller Kühlschrankinhalt (oben rechts):} Live-Anzeige aller im Kühlschrank befindlichen Objekte mit Mengenangabe.
    \item \textbf{Aktionsübersicht als Graph (links):} Balkendiagramm der häufigsten Objektinteraktionen.
    \item \textbf{Aktionslog-Tabelle (unten):} Chronologische Übersicht mit Zeitstempel, Aktionstyp und Objektklasse (z.\,B. ``Put in mayonnaise'', ``Take out ketchup'').
\end{enumerate}

\subsection{Websockets für Echtzeitaktualisierung}

Durch Integration eines Websocket-Mechanismus wird bei jeder neuen erkannten Aktion eine Push-Benachrichtigung vom Server an das Frontend gesendet.
Die Benutzeroberfläche aktualisiert sich automatisch, ohne dass manuelles Neuladen notwendig ist.

\subsection{Usability und Systemintegration}

Die Webanwendung ermöglicht eine intuitive Übersicht über den Systemzustand und erlaubt es, Veränderungen im Kühlschrankinhalt effizient nachzuvollziehen.
Die entkoppelte Architektur erlaubt zukünftig eine einfache Erweiterung, z.\,B. für Benutzerverwaltung oder mobile Zugriffe.

\section{Fazit}

Der von mir umgesetzte Teil des Projekts stellt die zentrale logische Infrastruktur bereit, um detektierte Objektbewegungen dauerhaft zu speichern und benutzerfreundlich zu visualisieren.
Die entwickelte SQLite-Datenbank bildet das Rückgrat des gesamten Systems und gewährleistet eine robuste, nachvollziehbare Datenspeicherung.
Die Weboberfläche erschließt diese Daten visuell und macht das komplexe System für Benutzer verständlich und praktisch nutzbar.

Diese Komponenten verbinden die technischen Ergebnisse der Objekterkennung mit einer funktionalen, alltagstauglichen Anwendung und leisten damit einen wesentlichen Beitrag zur ganzheitlichen Umsetzung unseres Studienprojekts.
