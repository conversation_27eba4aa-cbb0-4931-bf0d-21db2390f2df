%HEADER

%<---!!!!!!!!!!!!!!! MAKRO-DEFIONITIONEN; BITTE NICHT VERAENDERN !!!!!!!!!!
%<--- ARBEIT EINSEITIG
\def\makroEinseitig{
    %KOMA-Script-Klasse: scrreprt
    %deutsches Design, Schriftgröße 12, DIN A4
    %Literaturverzeichnis und Index in Inhaltsverzerzeichnis einbinden
    \documentclass[12pt,a4paper,bibliography=totoc,listof=totoc,oneside]{scrreprt}
    %Seitenspiegel einstellen
    \usepackage[a4paper]{geometry}
    \geometry{a4paper,left=30mm,right=25mm,
        bottom=20mm,top=15mm,bindingoffset=2mm,
        includehead,includefoot}}
% ARBEIT EINSEITIG --->

\def\makroZweiseitig{
    %<--- ARBEIT ZWEISEITIG
    %KOMA-Script-Klasse: scrreprt
    %deutsches Design, zweiseitig
    %Literaturverzeichnis und Index in Inhaltsverzerzeichnis einbinden
    \documentclass[12pt,a4paper,bibliography=totoc,listof=totoc,twoside, headsepline]{scrreprt}
    \usepackage[a4paper]{geometry}
    \geometry{a4paper,left=25mm,right=25mm,
        bottom=20mm,top=15mm,bindingoffset=2mm,
        includehead,includefoot}}
% ARBEIT ZWEISEITIG --->

%<--- Einstellungen Kopfzeile
\def\makroFH-Kopfzeilenstil{
\pagestyle{scrheadings}
\KOMAoption{headsepline}{0.4pt}
\pagestyle{scrheadings}
\renewcommand*{\chapterpagestyle}{scrheadings}}
%Einstellungen Kopfzeile --->
%!!!!!!!!!!!!!!! MAKRO-DEFIONITIONEN; BITTE NICHT VERAENDERN !!!!!!!!!!--->


%AUSWAHL: TEXT EINSEITIG (ja/nein)
\makroEinseitig
%\makroZweiseitig
\usepackage{enumitem}
\usepackage{pgfgantt}
\usepackage{array}
\newcolumntype{P}[1]{>{\centering\arraybackslash}p{#1}}

%schalte Umlaute frei
\usepackage[ngerman]{babel}
%passende Codierung
\usepackage[utf8]{inputenc}
%Seitenspiegel einzustellen
\usepackage[a4paper]{geometry}
%Mathepaket
\usepackage{amsmath}
%Symbole
\usepackage{amssymb}
%griechische Symbole
\usepackage{upgreek}
%weitere Symbole
\usepackage{pxfonts}
% Phonetischen Alphabete für LaTeX
%\usepackage{tipa}
%farbige Schriften
\usepackage{color}
\usepackage{scrhack}
%Bilder fixieren
\usepackage{float}
%Grafiken einbinden
\usepackage{graphicx}
% Kopf- und Fußzeilen
\usepackage[automark,pagestyleset=standard,markcase=used]{scrlayer-scrpage}
% deutsche Überschriften
\usepackage[ngerman]{translator}
% Kopfzeilenabstand festlegen
\setlength{\headheight}{10mm}
%Abb. statt Abbildung
\usepackage{caption3}
\addto\captionsngerman{
    \renewcommand{\figurename}{Abb.}
    \renewcommand{\tablename}{Tab.}
}
% Links in ToC etc aktivieren
\usepackage[hidelinks]{hyperref}
%Glossar-Pakage
\usepackage[
    nonumberlist, %keine Seitenzahlen anzeigen
    acronym,      %ein Abkürzungsverzeichnis erstellen
    toc]          %Einträge im Inhaltsverzeichnis      
{glossaries}
\usepackage{cite}
%Glossar einschalten
%\makeglossaries

\usepackage{dirtree}
\usepackage{listings}

% von Vlad (chatGPT*)
\usepackage{booktabs}
\usepackage[gen]{eurosym}
\usepackage[T1]{fontenc}
\usepackage{textcomp} % Für das €-Zeichen
\usepackage{newunicodechar}
\newunicodechar{≈}{\ensuremath{\approx}}

\definecolor{dkgreen}{rgb}{0,0.6,0}
\definecolor{gray}{rgb}{0.5,0.5,0.5}
\definecolor{mauve}{rgb}{0.58,0,0.82}

\lstset{frame=tb,
    language=Python,
    aboveskip=3mm,
    belowskip=3mm,
    showstringspaces=false,
    columns=flexible,
    basicstyle={\small\ttfamily},
    numbers=none,
    numberstyle=\tiny\color{gray},
    keywordstyle=\color{blue},
    commentstyle=\color{dkgreen},
    stringstyle=\color{mauve},
    breaklines=true,
    breakatwhitespace=true,
    tabsize=3
}
\renewcommand\lstlistingname{Repository}
\renewcommand\lstlistlistingname{Codeverzeichnis}

