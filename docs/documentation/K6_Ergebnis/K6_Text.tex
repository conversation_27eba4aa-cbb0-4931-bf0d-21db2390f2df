\chapter{Ergebnis}
Es wurde ein LSTM-Modell entwickelt, welches mithilfe der Keypoints trainiert wurde und abschließend eine 
categorical accuracy von 0,9 aufweisen konnte. Um dieses Ergebnis weiter zu verbessern, wurde das Corrnet
Modell verwendet. Dieses wurde aufgesetzt, dokumentiert und mit den eigenen Daten auf einen WER-Score von 
5,4 \% gefinetuned.

Um diese Ergebnisse praktisch umzusetzen, wurde eine Demo-Applikation entwickelt. Diese kann die beiden 
Modelle auf Bilder, die durch eine Webcam aufgenommen wurden, anwenden und die erzielten Ergebnisse auf
anschauliche Weise anzeigen. 
\begin{figure}[H]
    %\hspace*{-80px}
    \centering
    %\includegraphics[scale=0.198]{K6_Ergebnis/screen_small.png}
    \caption{Screenshot Demo Applikation}
\end{figure}

Zuletzt wurde ein Vergleich zwischen den beiden Modellen über die Funktionsweise und Ergebniskennzahlen gezogen,
welcher zeigte, dass das CorrNet das robustere und effizientere Modell ist.