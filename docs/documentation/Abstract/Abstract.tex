\begin{abstract}
\begin{center}
\Huge
\emph{\textbf{Abstract}}
\end{center}
\normalsize
\vspace{15mm}
\textit{In Deutschland gibt es etwa 300.000 Menschen mit Hörbeeinträchtigungen, die sich mit Gebärdensprache verständigen. Jedoch gibt es derzeit keine leistungsfähigen Sprachmodelle für die Übersetzung in oder aus Gebärdensprache, wie sie es für gesprochene Sprache gibt. In diesem Studienprojekt wurden zwei Modelle entwickelt, die das Ziel verfolgen, Bewegungen aus Videoaufnahmen von Gebärdensprache zu erkennen und diese in Text zu übersetzen. Dabei wurde für das erste Modell eigener Datensatz aufgenommen und darauf ein LSTM Modell trainiert. Im zweiten Ansatz wurde der öffentlich verfügbare Datensatz PHOENIX2014-T verwendet, auf dem bereits ein Modell mit einem Korrelationsmodul vortrainiert wurde. <PERSON><PERSON>uss<PERSON>lich wurden beide Ansätze in einer Demoapplikation eingebunden und ihre Ergebnisse verglichen.}
\end{abstract}