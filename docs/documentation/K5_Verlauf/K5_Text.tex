\chapter{<PERSON><PERSON><PERSON><PERSON>}
\section{Ist-/Sollvergleich: Projektplan}

\subsection{Soll-Verlauf}
Geplant war für den Projektablauf ein geradliniger Ablauf, bei dem jeder Abschnitt auf die Ergebnisse des Vorherigen aufbaut.
Es wurde eine Pause für die Klausurenphase einkalkuliert, wofür die verbleibenden Themen in die vorlesungsfreie Zeit geschoben
wurden.

\definecolor{barblue}{RGB}{153,204,254}
\definecolor{groupblue}{RGB}{51,102,254}
\definecolor{linkred}{RGB}{165,0,33}
\setganttlinklabel{f-s}{}
\newcounter{myWeekNum}
\stepcounter{myWeekNum}
\newcommand{\myWeek}{\themyWeekNum
    \stepcounter{myWeekNum}
    \ifnum\themyWeekNum=53
        \setcounter{myWeekNum}{1}
    \else\fi
}
%German abbreviations; Use S. instead of September  
\def\pgfcalendarmonthname#1{%  
\ifcase#1 Dezember \or Jan.\or Feb.\or März\or April\or Mai\or Juni\or Juli \or Aug.\or September \or Oktober \or Nov. \or Dez.\fi%  
}  
\setcounter{myWeekNum}{45}
\ganttset{calendar week text={\myWeek{}}}
\begin{figure}[H]
    \hspace*{-65px}
    \begin{ganttchart}[
            canvas/.append style={fill=none, draw=black!5, line width=.75pt},
            hgrid style/.style={draw=black!5, line width=.75pt},
            vgrid={*{2}{draw=none},{draw=black!5, line width=.75pt},*{4}{draw=none},},
            x unit=0.45mm,
            y unit chart=0.6cm,
            y unit title=0.5cm,
            time slot format=isodate,
            title height=1,
            title/.style={draw=black!5, fill=none},
            title label font=\bfseries\footnotesize,
            bar label font=\mdseries\small\color{black!70},
            bar label node/.append style={left=0.5cm},
            bar/.append style={draw=none, fill=black!63},
            bar incomplete/.append style={fill=barblue},
            bar progress label font=\mdseries\footnotesize\color{black!70},
            group incomplete/.append style={fill=groupblue},
            group left shift=0,
            group right shift=0,
            group height=.5,
            group peaks tip position=0,
            group label node/.append style={left=0.5cm},
            group progress label font=\bfseries\small,
            link/.style={-latex, line width=1.5pt, linkred},
            link label font=\scriptsize\bfseries,
            link label node/.append style={below left=-2pt and 0pt},
        ]{2024-11-01}{2025-08-30}

        \gantttitlecalendar{year, month=name} \\
        \ganttmilestone{Kickoff}{2024-11-04} \\
        \ganttbar{Ansatz entwickeln}{2024-11-05}{2024-12-20}\\
        \ganttbar[name=D1]{Trainingsdaten}{2024-12-21}{2025-01-20}
        \ganttbar{}{2025-02-14}{2025-03-01}\\
        \ganttbar[name=D]{Objekterkennung}{2025-03-02}{2025-04-01} \\
        \ganttbar[]{Aktionserkennung}{2025-04-02}{2025-04-15}\\
        \ganttbar[name=M]{Inventarüberwachung}{2025-04-16}{2025-04-24} \\
        \ganttbar[name=M1]{User Interface}{2025-04-25}{2025-06-25}\\
        \ganttbar[name=U]{Türsensor}{2025-06-26}{2025-07-26} \\
        \ganttbar{Dokumentation}{2025-07-27}{2025-08-27} \\
        \ganttmilestone{Abgabe}{2025-08-27}
    \end{ganttchart}
    \caption{Soll Gantt-Ablaufdiagram}
\end{figure}

\subsection{Ist-Verlauf}
Der tatsächliche Projektverlauf unterscheidet sich von dem geplanten Ablauf haupt\-sächlich dadurch, dass die einzelnen
Phasen nicht nacheinander starten, sondern bereits dann, sobald genug Ergebnisse vorlagen. Dies geschah zum
einen, um die Effizienz zu steigern, zum anderen, weil der anfängliche Plan einer sehr naiven Struktur folgte.

Der Beginn des Projekts verzögerte sich um einige Wochen, da durch Organisationsprobleme erst noch ein Ort für die 
Aufnahmen gefunden werden musste.

Das Objekterkennungsmodell stand bereits relativ früh fest, wurde jedoch den Großteil des Projekts kontinuierlich durch
neue Daten und synthetische Daten neutraniert und verbessert. 

Zu Beginn des Projekts war die Einbindung eines Türsensors in das Projekt geplant. Diese wurde jedoch später aufgrund der
Komplexität und des zeitliichen Aufwands ausgeschlossen. 

\setcounter{myWeekNum}{45}
\ganttset{calendar week text={\myWeek{}}}
\begin{figure}[H]
    \hspace*{-65px}
    \begin{ganttchart}[
            canvas/.append style={fill=none, draw=black!5, line width=.75pt},
            hgrid style/.style={draw=black!5, line width=.75pt},
            vgrid={*{2}{draw=none},{draw=black!5, line width=.75pt},*{4}{draw=none},},
            x unit=0.45mm,
            y unit chart=0.6cm,
            y unit title=0.5cm,
            time slot format=isodate,
            title height=1,
            title/.style={draw=black!5, fill=none},
            title label font=\bfseries\footnotesize,
            bar label font=\mdseries\small\color{black!70},
            bar label node/.append style={left=0.5cm},
            bar/.append style={draw=none, fill=black!63},
            bar incomplete/.append style={fill=barblue},
            bar progress label font=\mdseries\footnotesize\color{black!70},
            group incomplete/.append style={fill=groupblue},
            group left shift=0,
            group right shift=0,
            group height=.5,
            group peaks tip position=0,
            group label node/.append style={left=0.5cm},
            group progress label font=\bfseries\small,
            link/.style={-latex, line width=1.5pt, linkred},
            link label font=\scriptsize\bfseries,
            link label node/.append style={below left=-2pt and 0pt},
        ]{2024-11-01}{2025-08-30}

        \gantttitlecalendar{year, month=name} \\
        \ganttmilestone{Kickoff}{2024-11-04} \\
        \ganttbar{Ansatz entwickeln}{2024-12-01}{2025-01-01}\\
        \ganttbar{Aktionserkennung}{2025-01-20}{2025-02-11} \\
        \ganttmilestone{MVP}{2025-02-11} \\
        \ganttbar[name=D1]{Trainingsdaten}{2025-01-01}{2025-03-01}\\
        \ganttbar{Objekterkennung}{2025-02-20}{2025-03-25} \\
        \ganttbar{Datenbank}{2025-03-04}{2025-03-20} \\
        \ganttbar{Synthetische Daten}{2025-04-09}{2025-05-19} \\
        \ganttbar{User Interface}{2025-02-15}{2025-05-15} \\
        \ganttbar{Evaluation}{2025-04-25}{2025-05-19} \\
        \ganttmilestone{PI setup}{2025-06-19} \\
        \ganttbar{Dokumentation}{2025-07-27}{2025-08-27} \\
        \ganttmilestone{Abgabe}{2025-08-27}
    \end{ganttchart}
    \caption{Soll Gantt-Ablaufdiagram}
\end{figure}

\newpage
\section{Projektablauf}
\subsection{MVP}
mvp sollte nur eine rudimentäre Objekterkennung und Aktionserkennung beinhalten, um auf etwas aufbauen zu können

Hardware: über usb angeschlossene logitech webcam an workstation

hier wurde für die Objekterkennung ein YOLOv8 Modell verwendet, welches auf den COCO-Datensatz trainiert wurde (und die dort vorhandenen klassen verwendet)
aktionserkennung per python skript, welches die bounding boxen der objekte verwendet, und den output auf der console ausgibt

testdaten waren Videos von der webcam, mit den Klassen (apple und banana)

\subsection{Trainingsdaten}
Um das Modell auf eigene Objekte zu trainieren, wurden Trainingsdaten aufgenommen.
Die Aufnahmeumgebung war ein Schrank mit mehreren Böden, auf denen die Objekte platziert wurden.
Der Scope des Projekts wurde auf 9 Klassen begrenz, um die Komplexität zu reduzieren und eine schnelle Entwicklung zu ermöglichen.
Die Klassen sind: 
0: 'bacon',
1: 'carrot',
2: 'cucumber',
3: 'egg',
4: 'eggs',
5: 'ketchup',
6: 'mayonnaise',
7: 'mustard',
8: 'yogurt'

\begin{figure}[H]
    \centering
    %\includegraphics[scale=0.4]{K5_Verlauf/diagrammLSTM.jpg}
    \caption{Beispiel Trainingsdaten}
\end{figure}

Aus den aufgenommenen Videos wurden die Frames extrahiert und mit Hilfe von XXX annotiert. Dabei wurden zuert über alle Klassen hinweg XXX Bilder annotiert, um mit diesen ein erstes Modell zu trainieren, welches für die weiteren Annotations als Basis dienen sollte. Nachdem die vorannotierten Bilder manuell überprüft und korrigiert wurden, wurde das Modell mit diesen trainiert.

Bei der Überprüfung des Modells wurde festgestellt, dass die Objekterkennung noch nicht stabil genug war, um eine zuverlässige Aktionserkennung zu ermöglichen. Daher wurde beschlossen, weitere Trainingsdaten aufzunehmen und das Modell weiter zu verbessern.

\subsection{Synthetische Daten}
Um die Datenmenge für das Training des Modells zu erhöhen ohne weitere realde Daten aufnehmen und labeln zu müssen, wurden synthetische Daten generiert.
Dazu wurde ein Skript entwickelt, welches die aufgenommenen Bilder durch Augmentationsmethoden wie Rotation, Skalierung, Helligkeit und Kontrast verändert. Es wurden dabei verschiedene Kombinationen dieser Methoden verwendet, um eine Vielzahl von Bildern zu generieren, die das Modell auf verschiedene Szenarien vorbereiten.

Für die Evaluation wurde das Modell sowohl auf rein synthetischen Daten als auch auf einem Mix aus synthetischen und realen Daten getestet. In beiden Fällen waren die Ergebnisse unbefriedigend: Das Modell zeigte auf synthetischen Daten zwar eine hohe Genauigkeit, konnte diese Leistung jedoch nicht auf reale Daten übertragen. Auch der Mix aus synthetischen und realen Trainingsdaten führte nicht zu einer signifikanten Verbesserung der Erkennungsleistung auf echten Bildern. Dies deutet darauf hin, dass die synthetisch generierten Daten die Varianz und Komplexität der realen Szenarien nicht ausreichend abbilden konnten.

\subsection{Verbesserung der Objekterkennung}
Um im nächsten Schritt die Objekterkennung weiter zu verbessern und vor allem die bisher aufgetretenen false positives zu reduzieren, wurde der Trainingsdatensatz um weitere Bilder erweitert. Diese enthielten Bilder, die Objekte enthielten welche nicht zu den Klassen gehörten, die das Modell erkennen sollte. Dadurch sollte das Modell lernen, diese Objekte nicht als relevante Objekte zu erkennen und somit die Anzahl der false positives zu reduzieren.


\subsection{Evaluation}
Für eine qualitative Evaluation des Modells wurde ein Testdatensatz erstellt, der aus Videos besteht, die nicht im Traiingsdatensatz enthalten sind. Dabei wurden sowohl die gleichen Marken als auch neue Marken verwendet, um die Robustheit des Modells zu testen.

Die Evaluation wurde dabei über die von ultralytics bereitgestellten Funktionen durchgeführt, die eine Auswertung der Precision, Recall und F1-Scores ermöglichen.

Jeder Schritt in der Modellentwicklugn wurde dabei mit diesen Metriken evaluiert, um die Fortschritte zu messen und die besten Modelle auszuwählen. Diese Messungen zeigten deutliche Unterschiede in der Leistung des Modells zwischen den Validation und Testsätzen. Dies deutet darauf hin, dass das Modell zwar in der Lage ist, die Objekte in den Trainingsdaten zu erkennen, jedoch Schwierigkeiten hat, diese Leistung auf neue, unbekannte Daten zu übertragen. Um diese These zu überprüfen, wurden die Evaluationsdaten in zwei Gruppen unterteilt: eine Gruppe mit bekannten Marken und eine Gruppe mit unbekannten Marken. Die Ergebnisse zeigten, dass das Modell bei den bekannten Marken eine hohe Genauigkeit erzielte, jedoch bei den unbekannten Marken deutlich schlechter abschnitt. Dies deutet darauf hin, dass das Modell nicht in der Lage ist, die Objekte zu generalisieren und somit auf neue Daten zu übertragen.

\subsection{Datenbank und User Interface}
Um die Objekterkennung und Aktionserkennung in eine Anwendung zu integrieren, wurde eine Datenbank aufgesetzt, die die erkannten Objekte und Aktionen speichert.
Die Datenbank wurde mit SQLite implementiert, um eine einfache und schnelle Lösung zu bieten 

Für das UserInterface wurden zuerst Wireframes erstellt, um die Benutzerfühgrung zu planen. 
\begin{figure}[H]
    \centering
    %\includegraphics[scale=0.4]{K5_Verlauf/diagrammLSTM.jpg}
    \caption{Wireframes für das User Interface}
\end{figure}
Diese wurden dann in einer Webanwendung umgesetzt, die mit Vue.js, TypeScript Nodejs entwickelt wurde.
\begin{figure}[H]
    \centering
    %\includegraphics[scale=0.4]{K5_Verlauf/diagrammLSTM.jpg}
    \caption{Screeenshot der Webanwendung}
\end{figure}

\subsection{Deployment auf Raspberry Pi}

\subsection{Kosten- und Systemvergleich mit cloudbasierten Lösungen}

Zur Einordnung der Effizienz unserer Eigenentwicklung wurde ein Vergleich mit gängigen cloudbasierten Objekterkennungslösungen wie Amazon Rekognition, Google Cloud Vision und Microsoft Azure Computer Vision durchgeführt. Dabei wurden sowohl die laufenden Betriebskosten (insbesondere Stromverbrauch) als auch die Systemkomplexität berücksichtigt.

\subsubsection*{Strom- und Hardwarekosten unseres Systems}

Unser System basiert auf einem lokal betriebenen Raspberry Pi 5 mit Kamera, aktivem Kühlgehäuse und KI-Beschleuniger (Raspberry Pi AI HAT+).
Die Stromkosten lassen sich wie folgt abschätzen:

\begin{itemize}
    \item \textbf{Annahme:} Der Kühlschrank wird im Schnitt 15 Minuten pro Tag geöffnet.
    \item \textbf{Volllastverbrauch (während Öffnung):} ca.\ 15\,W
    \item \textbf{Leerlaufverbrauch (Restzeit):} ca.\ 3\,W
    \item \textbf{Strompreis Deutschland (2025):} ca.\ 0{,}40\,€/kWh\footnote{Quelle: BDEW Strompreisanalyse 2025, \url{https://www.bdew.de/service/daten-und-grafiken/bdew-strompreisanalyse/}}
\end{itemize}

\textbf{Jahresverbrauch:}
\begin{align*}
\text{Volllast:} &\quad 365 \times \frac{15}{60}~\text{h} \times 7\,\text{W} = 1\,368{,}75\,\text{Wh} \\
\text{Standby:} &\quad 365 \times \frac{1425}{60}~\text{h} \times 3\,\text{W} = 26\,006{,}25\,\text{Wh} \\
\text{Gesamt:} &\quad 1\,368{,}75\,\text{Wh} + 26\,006{,}25\,\text{Wh} = 27{,}375\,\text{kWh}
\end{align*}

\textbf{Jährliche Stromkosten:}
\[
27{,}375\,\text{kWh} \times 0{,}40\,€/kWh = \textbf{10{,}95\,€}
\]

\textbf{Initialkosten Hardware:}
\begin{itemize}
    \item Raspberry Pi 5: ca. 130\,€
    \item Raspberry Pi AI HAT+: ca. 130\,€
    \item Kamera: ca. 50\,€
    \item \textbf{Gesamt:} \textbf{310\,€} einmalig
\end{itemize}

\subsubsection*{Kosten cloudbasierter Dienste}

Cloudanbieter wie Amazon Rekognition berechnen typischerweise pro verarbeiteten Frame:

\begin{itemize}
    \item \textbf{Bounding Box Detection:} ca. 0{,}0012\,\$ pro Bild (Amazon Rekognition)
    \item \textbf{Rechenbeispiel:} 15 Minuten pro Tag $\times$ 300 Bilder/Minute $\times$ 365 Tage = 1\,642\,500 Bilder/Jahr
\end{itemize}

\textbf{Jährliche Kosten:}
\[
1\,642\,500 \times 0{,}0012\,\text{\$} = \textbf{1\,971\,\$} \approx 1\,695\,€\footnote{Umrechnungskurs: 1\,\$ ≈ 0{,}86\,€}
\]

Dazu kommen weiterhin:

\begin{itemize}
    \item Zusätzliche Latenz für Datenübertragung
    \item Laufender Internetzugang notwendig
    \item Lokale Hardware weiterhin nötig (z.\,B. Raspberry zur Bildaufnahme)
\end{itemize}

\subsubsection*{Qualitativer Vergleich}

Im Vergleich zur Eigenentwicklung zeigen cloudbasierte Modelle bei Anwendung auf unser reales, domänenspezifisches Videomaterial deutliche Schwächen: Beispielsweise konnte Amazon Rekognition bei Tests mit unseren Videoaufnahmen keine der neun verwendeten Klassen korrekt klassifizieren.
Eine Verwendung dieser Dienste würde daher erfordern:

\begin{itemize}
    \item Eigenständiges, aufwendiges Modelltraining mit unseren Datensätzen
    \item Weiterhin lokale Hardware zur Bilderfassung und Vorverarbeitung
    \item Komplexere Systemarchitektur (Streaming, Fehlerhandling, Upload)
\end{itemize}

\subsubsection*{Fazit}

Unsere Eigenentwicklung bietet im Vergleich zu großen Cloudanbietern folgende Vorteile:

\begin{itemize}
    \item \textbf{Deutlich geringere laufende Kosten:} ca.\ 10{,}66\,€\,/\,Jahr vs.\ über 1\,800\,€\,/\,Jahr
    \item \textbf{Volle Kontrolle über Datenverarbeitung und Speicherung}
    \item \textbf{Geringere Komplexität:} Lokale Verarbeitung ohne Uploads oder externe Abhängigkeiten
    \item \textbf{Höhere Anfangsinvestitionskosten:} KI-Beschleuniger ist nötig, ca.\ 100--150\,€ zusätzlich
\end{itemize}

Somit ergibt sich ein klarer Vorteil hinsichtlich Kosten, Kontrolle und Systemschlichtheit.
Die Cloudlösungen bieten zwar Skalierbarkeit, jedoch zu signifikant höheren Kosten und mit reduziertem Nutzen im spezifischen Anwendungsfall.

\section{Aufgetretene Herausforderungen}

\begin{itemize}
    \item Objekterkennung für unbekannte Marken
    \item Raspberry Halo
\end{itemize}