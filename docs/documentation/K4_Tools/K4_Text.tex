\chapter{Verwendete Tools}

\begin{itemize}
    \item Entwicklungsumgebung: Visual Studio Code
    \item Build-Tools/-Umgebungen: anaconda, uv
    \item Frameworks: opencv, ultralytics, matplotlib, pytorch, OpenGL
    \item Versionsverwaltung: Git, GitLab
    \item Dokumentation: Latex, bibtex
    \item Kommunikation: Discord, Whatsapp
\end{itemize}

Verwendete Conda Umgebungen sind Teil des Repositorys und sind dort als .yml Dateien abgelegt. In diesen sind die Versionen der Frameworks festgelegt.