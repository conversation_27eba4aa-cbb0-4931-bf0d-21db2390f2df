\chapter{Fazit}
Unser Ziel ein Modell zu entwickeln, das einfache Gebärden und kurze Sequenzen von Gebärden zu Text übersetzen kann, wurde mit dem CorrNet erreicht. Mit dem selbst aufgenommenen Datensatz und dem entwickelten LSTM hat das Team längere Zeit verbracht, die uns dem Ziel zwar nicht wesentlich näher gebracht hat, jedoch konnten wir daraus viele technische Learnings ziehen und vor allem viel Kontextwissen erlangen, das für den Projektablauf maßgeblich war. Rückblickend war die Aufnahme des eigenen Datensatzes ohne das nötige Kontextwissen kein guter Ansatz, da dadurch zu viel Zeit verloren ging und die Daten durch das fehlende Wissen nicht die nötige Qualität aufweisen. Deshalb würden wir in Zukunft mehr Zeit in die Recherche nach bestehenden Datensätzen stecken, um die Qualitätsanforderungen zu treffen.
Auf diesem Projekt können in Zukunft weitere Projekte aufbauen, die das langfristige Ziel, eine mobile App mit eingebundener KI zu entwickeln, verfolgen. Dafür kann das vom Team entwickelte Modell nochmals gefinetuned werden, um dann längere Sequenzen damit übersetzen zu können. Durch die Aufnahme von Daten zusammen mit Menschen, die Gebärden sprechen, kann das Modell zusätzlich verbessert werden.
