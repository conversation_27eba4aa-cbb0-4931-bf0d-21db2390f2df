
@misc{nicholas_renotte_sign_2021,
  title        = {Sign {Language} {Detection} using {ACTION} {RECOGNITION} with {Python} {\textbar} {LSTM} {Deep} {Learning} {Model}},
  url          = {https://www.youtube.com/watch?v=doDUihpj6ro},
  howpublished = {\url{https://www.youtube.com/watch?v=doDUihpj6ro}},
  urldate      = {2023-05-16},
  author       = {{<PERSON>}},
  month        = jun,
  year         = {2021}
}

@misc{hu2023continuous,
  title     = {Continuous Sign Language Recognition with Correlation Network},
  author    = {Hu, Lianyu and Gao, Li<PERSON> and <PERSON>, Zekang and <PERSON>, Wei},
  booktitle = {Proceedings of the IEEE/CVF International Conference on Computer Vision},
  year      = {2023}
}

@article{koller_continuous_2015,
  title      = {Continuous sign language recognition: {Towards} large vocabulary statistical recognition systems handling multiple signers},
  volume     = {141},
  issn       = {10773142},
  shorttitle = {Continuous sign language recognition},
  url        = {https://linkinghub.elsevier.com/retrieve/pii/S1077314215002088},
  doi        = {10.1016/j.cviu.2015.09.013},
  language   = {en},
  urldate    = {2023-08-02},
  journal    = {Computer Vision and Image Understanding},
  author     = {<PERSON>ller, <PERSON> and Forster, Jens and Ney, <PERSON>},
  month      = dec,
  year       = {2015},
  pages      = {108--125}
}

@misc{yuan_bighand2.2m_2017,
  title      = {{BigHand2}.{2M} {Benchmark}: {Hand} {Pose} {Dataset} and {State} of the {Art} {Analysis}},
  shorttitle = {{BigHand2}.{2M} {Benchmark}},
  url        = {http://arxiv.org/abs/1704.02612},
  abstract   = {In this paper we introduce a large-scale hand pose dataset, collected using a novel capture method. Existing datasets are either generated synthetically or captured using depth sensors: synthetic datasets exhibit a certain level of appearance difference from real depth images, and real datasets are limited in quantity and coverage, mainly due to the difficulty to annotate them. We propose a tracking system with six 6D magnetic sensors and inverse kinematics to automatically obtain 21-joints hand pose annotations of depth maps captured with minimal restriction on the range of motion. The capture protocol aims to fully cover the natural hand pose space. As shown in embedding plots, the new dataset exhibits a significantly wider and denser range of hand poses compared to existing benchmarks. Current state-of-the-art methods are evaluated on the dataset, and we demonstrate significant improvements in cross-benchmark performance. We also show significant improvements in egocentric hand pose estimation with a CNN trained on the new dataset.},
  urldate    = {2023-08-13},
  publisher  = {arXiv},
  author     = {Yuan, Shanxin and Ye, Qi and Stenger, Bjorn and Jain, Siddhant and Kim, Tae-Kyun},
  month      = dec,
  year       = {2017},
  note       = {arXiv:1704.02612 [cs]},
  keywords   = {Computer Science - Computer Vision and Pattern Recognition}
}


@misc{geitgey_face_2023,
  title        = {Face {Recognition}},
  copyright    = {MIT},
  url          = {https://github.com/ageitgey/face_recognition},
  howpublished = {\url{https://github.com/ageitgey/face_recognition}},
  abstract     = {The world's simplest facial recognition api for Python and the command line},
  urldate      = {2023-08-13},
  author       = {Geitgey, Adam},
  month        = mar,
  year         = {2017},
  keywords     = {face-detection, face-recognition, machine-learning, python}
}

@misc{noauthor_stereolabs_2023,
  title        = {Stereolabs {ZED} - {Using} multiple {ZED}},
  url          = {https://github.com/stereolabs/zed-multi-camera},
  howpublished = {\url{https://github.com/stereolabs/zed-multi-camera}},
  abstract     = {ZED SDK sample with multiple ZEDs},
  urldate      = {2023-08-13},
  author       = {Stereolabs},
  publisher    = {Stereolabs},
  month        = feb,
  year         = {2016},
  keywords     = {stereo-vision, zed-camera}
}

@misc{stereolabs_svo_export_2023,
	title        = {Stereolabs ZED - SVO Export},
	url          = {https://github.com/stereolabs/zed-sdk/tree/master/recording/export/svo/python},
  howpublished = {\url{https://github.com/stereolabs/zed-sdk/tree/master/recording/export/svo/python}},
	urldate      = {2023-09-09},
  author       = {Stereolabs},
  publisher    = {Stereolabs},
  month        = apr,
  year         = {2023},
  keywords     = {stereo-vision, zed-camera}
}

@misc{dano_answer_2014,
	title        = {Answer to "{Multiprocessing} working in {Python} but not in {iPython}"},
	url          = {https://stackoverflow.com/a/23641560},
	howpublished = {\url{https://stackoverflow.com/a/23641560}},
	urldate      = {2023-09-05},
	journal      = {Stack Overflow},
	author       = {dano},
	month        = may,
	year         = {2014},
}