# kuehlschrank_id

## Target
Monitoring and inventorying of a refrigerator using AI image recognition models.

## Design
### Update 1
* Detect door opening / closing action based on hand bones and image data
    * Video data is segmented into 2s windows to allow multiple actions (?)
* While door is open, detect action types (input / output / none) based on bounding box of food detected using CNN (fine tune YOLO)
    * Continuously detect food, segment based on visibility of the food class
    * Disappearance marks end of a segment
    * Start off by looking at bounding box movement programmatically (future extension: model based prediction)

### Update 2
* Detect only objects (food)
* Don't detect open / close for now

### Update 3
* calculate actions from detected bounding boxes

### Update 4
* UI concept
* test tracking model

### Update 5
* recorded & labeled own data -> trained model
* UI architecture ready

### Update 6
* Test on new data: eggs often detected as bacon -> why
* DB-connection script ready

## Current classes
```
0: 'bacon',
1: 'carrot',
2: 'cucumber',
3: 'egg',
4: 'eggs',
5: 'ketchup',
6: 'mayonnaise',
7: 'mustard',
8: 'yogurt'
```

## Usage
- Recording data using `datasets/record_camera.py`, label it using labelstudio. From labelstudio export label as json.
- Train a model using the recorded data and the exported json in `train_model.ipynb`
- Launch the monitoring service and UI using `todo.py`

To run the detection model use `uv run app`.

## Training Results
### Models
|Model name|Data|
|---|---|
|`yolo_all_train.pt`|manual labeled data without errors|
|`yolo_mix_synth_real.pt`|all manual labeled data mixed with synthetic (w. occlusion) data (70-30)|
|`yolo_synthetic_with_occlusion.pt`|only synthetic data with occlusion augmentation|
|`yolo_synthetic_without_occlusion.pt`|only synthetic data without occlusion augmentation|
|`yolo_mix_synth_background.pt`|all manual labeled data mixed with synthetic (w. occlusion, w. different backgrounds) data (70-30)|

### Training Runs (Tensorboard)
|Run|Data|
|---|---|
|train4|manual labeled data without errors |
|train4|synthetic data without occlusion augmentation |
|train4|synthetic data with occlusion augmentation|
|train9|mixed synthetic and real data (70-30) |
|train8|mixed synthetic and real data (70-30) with different backgrounds in synthetic data|
|train10|mixed syntheticand real data with null images|
|train11|mixed synthetic (with multi object) and real data with null images|