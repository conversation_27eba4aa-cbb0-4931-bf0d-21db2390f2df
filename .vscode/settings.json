{
    "latex-workshop.latex.recipes": [
      {
        "name": "pdflatex -> bibtex -> pdflatex",
        "tools": [
          "pdflatex",
          "bibtex",
          "pdflatex"
        ]
      },
      {
        "name": "pdflatex -> makeglossary -> bibtex -> pdflatex",
        "tools": [
          "pdflatex",
          "makeglossaries",
          "bibtex",
          "pdflatex"
        ]
      },
      {
        "name": "pdflatex",
        "tools": [
          "pdflatex"
        ]
      },
    ],
    "latex-workshop.latex.tools":[
      {
        "name": "latexmk",
        "command": "latexmk",
        "args": [
          "-synctex=1",
          "-interaction=nonstopmode",
          "-file-line-error",
          "-pdf",
          "-outdir=%OUTDIR%",
          "%DOC%"
        ],
        "env": {}
      },
      {
        "name": "pdflatex",
        "command": "pdflatex",
        "args": [
          "-synctex=1",
          "-interaction=nonstopmode",
          "-file-line-error",
          "%DOC%"
        ],
        "env": {}
      },
      {
        "name": "bibtex",
        "command": "bibtex",
        "args": [
          "%DOCFILE%"
        ],
        "env": {}
      },
      {
          "name": "makeglossaries",
          "command": "makeglossaries",
          "args": [
            "%DOCFILE%"
          ]
        }
  ],
}