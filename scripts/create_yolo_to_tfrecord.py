#!/usr/bin/env python

import argparse
import os
import random
from pathlib import Path

import numpy as np
import tensorflow as tf
from PIL import Image
from tqdm import tqdm

from hailo_model_zoo.utils import path_resolver

TF_RECORD_TYPE = "val2017", "calib2017"
TF_RECORD_LOC = {
    "val2017": "models_files/coco/2023-08-03/coco_val2017.tfrecord",
    "calib2017": "models_files/coco/2023-08-03/coco_calib2017.tfrecord",
}


def _int64_feature(values):
    if not isinstance(values, (tuple, list)):
        values = [values]
    return tf.train.Feature(int64_list=tf.train.Int64List(value=values))


def _bytes_feature(values):
    return tf.train.Feature(bytes_list=tf.train.BytesList(value=[values]))


def _float_list_feature(value):
    return tf.train.Feature(float_list=tf.train.FloatList(value=value))


def parse_yolo_label(label_file):
    boxes = []
    with open(label_file, 'r') as f:
        for line in f:
            parts = line.strip().split()
            if len(parts) != 5:
                continue
            class_id, x_center, y_center, width, height = map(float, parts)
            boxes.append({
                "class_id": int(class_id),
                "x_center": x_center,
                "y_center": y_center,
                "width": width,
                "height": height
            })
    return boxes


def get_yolo_img_labels(images_dir, labels_dir):
    image_paths = sorted(Path(images_dir).glob("*.png"))
    files = []
    for i, img_path in enumerate(image_paths):
        label_path = Path(labels_dir) / (img_path.stem + ".txt")
        if label_path.exists():
            boxes = parse_yolo_label(label_path)
        else:
            boxes = []
        files.append((str(img_path), boxes))
    random.seed(0)
    random.shuffle(files)
    imgs_name2id = {Path(p).name: idx for idx, (p, _) in enumerate(files)}
    return files, imgs_name2id


def _create_tfrecord(filenames, name, num_images, imgs_name2id):
    tfrecords_filename = path_resolver.resolve_data_path(TF_RECORD_LOC[name])
    tfrecords_filename.parent.mkdir(parents=True, exist_ok=True)

    progress_bar = tqdm(filenames[:num_images])
    with tf.io.TFRecordWriter(str(tfrecords_filename)) as writer:
        for i, (img_path, boxes) in enumerate(progress_bar):
            progress_bar.set_description(f"{name} #{i+1}: {img_path}")
            img_jpeg = open(img_path, "rb").read()
            img = np.array(Image.open(img_path))
            image_height, image_width = img.shape[:2]

            xmin, xmax, ymin, ymax, category_id, is_crowd, area = [], [], [], [], [], [], []

            for box in boxes:
                x_center = box["x_center"]
                y_center = box["y_center"]
                width = box["width"]
                height = box["height"]

                x = x_center - width / 2
                y = y_center - height / 2

                if width <= 0 or height <= 0 or x < 0 or y < 0 or (x + width) > 1.0 or (y + height) > 1.0:
                    continue

                xmin.append(x)
                xmax.append(x + width)
                ymin.append(y)
                ymax.append(y + height)
                category_id.append(box["class_id"])
                is_crowd.append(0)
                area.append(width * height)

            img_id = imgs_name2id[os.path.basename(img_path)]

            example = tf.train.Example(
                features=tf.train.Features(
                    feature={
                        "height": _int64_feature(image_height),
                        "width": _int64_feature(image_width),
                        "num_boxes": _int64_feature(len(boxes)),
                        "image_id": _int64_feature(img_id),
                        "xmin": _float_list_feature(xmin),
                        "xmax": _float_list_feature(xmax),
                        "ymin": _float_list_feature(ymin),
                        "ymax": _float_list_feature(ymax),
                        "area": _float_list_feature(area),
                        "category_id": _int64_feature(category_id),
                        "is_crowd": _int64_feature(is_crowd),
                        "image_name": _bytes_feature(str.encode(os.path.basename(img_path))),
                        "image_jpeg": _bytes_feature(img_jpeg),
                    }
                )
            )
            writer.write(example.SerializeToString())
    return i + 1


def run(images_dir, labels_dir, name, num_images):
    if not images_dir or not labels_dir:
        raise ValueError("Both --img and --labels directories must be provided for YOLO dataset.")
    img_labels_list, imgs_name2id = get_yolo_img_labels(images_dir, labels_dir)
    images_num = _create_tfrecord(img_labels_list, name, num_images, imgs_name2id)
    print(f"\nDone converting {images_num} images")


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("type", help=f"which tf-record to create {TF_RECORD_TYPE}")
    parser.add_argument("--img", "-img", required=True, help="Path to image directory")
    parser.add_argument("--labels", "-labels", required=True, help="Path to YOLO labels directory")
    parser.add_argument("--num-images", type=int, default=8192, help="Limit number of images")
    args = parser.parse_args()

    assert args.type in TF_RECORD_TYPE, f"Invalid TFRecord type. Choose from {TF_RECORD_TYPE}"
    run(args.img, args.labels, args.type, args.num_images)
