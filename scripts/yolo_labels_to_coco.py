import os
import j<PERSON>
from PIL import Image
from datetime import datetime

# Directory containing txt and image files
ANNOTATION_DIR = 'datasets/dataset/labels/train'
IMAGE_DIR = 'datasets/dataset/images/train'  # where the .jpg images are stored
OUTPUT_FILE = 'datasets/dataset/annotations_coco.json'

# Initialize COCO structure
coco = {
    "info": {
        "description": "COCO 2017 Dataset",
        "url": "http://cocodataset.org",
        "version": "1.0",
        "year": 2017,
        "contributor": "HAW Landshut",
        "date_created": datetime.today().strftime('%Y/%m/%d')
    },
    "licenses": [
        {
            "url": "http://creativecommons.org/licenses/by/2.0/",
            "id": 4,
            "name": "Attribution License"
        }
    ],
    "images": [],
    "annotations": [],
    "categories": []
}

# Trackers
image_id = 0
annotation_id = 0
class_name_to_id = {}

# Loop over txt files
for filename in os.listdir(ANNOTATION_DIR):
    if filename.endswith('.txt'):
        txt_path = os.path.join(ANNOTATION_DIR, filename)
        image_filename = filename.replace('.txt', '.png')
        image_path = os.path.join(IMAGE_DIR, image_filename)

        if not os.path.exists(image_path):
            print(f"Warning: Image {image_filename} not found. Skipping.")
            continue

        # Open image to get dimensions
        with Image.open(image_path) as img:
            width, height = img.size

        # Add image info
        image_entry = {
            "id": image_id,
            "license": 4,
            "width": width,
            "height": height,
            "file_name": image_filename,
            "date_captured": "2013-11-15 02:41:42"  # or generate dynamically
        }
        coco["images"].append(image_entry)

        # Read annotation lines
        with open(txt_path, 'r') as f:
            lines = f.readlines()

        for line in lines:
            parts = line.strip().split()
            if len(parts) != 5:
                continue
            class_name, x_center, y_center, box_width, box_height = parts
            x_center, y_center, box_width, box_height = map(float, [x_center, y_center, box_width, box_height])

            # Convert to COCO format (absolute values)
            x = (x_center - box_width / 2) * width
            y = (y_center - box_height / 2) * height
            w = box_width * width
            h = box_height * height

            area = w * h

            # Assign a category_id
            if class_name not in class_name_to_id:
                class_id = len(class_name_to_id)
                class_name_to_id[class_name] = class_id
                coco["categories"].append({
                    "id": class_id,
                    "name": class_name,
                    "supercategory": "food"
                })
            else:
                class_id = class_name_to_id[class_name]

            # Add annotation
            annotation = {
                "id": annotation_id,
                "image_id": image_id,
                "category_id": class_id,
                "bbox": [x, y, w, h],
                "area": area,
                "iscrowd": 0,
                "segmentation": []
            }
            coco["annotations"].append(annotation)
            annotation_id += 1

        image_id += 1

# Save JSON
with open(OUTPUT_FILE, 'w') as f:
    json.dump(coco, f, indent=4)

print(f"COCO annotations saved to {OUTPUT_FILE}")
