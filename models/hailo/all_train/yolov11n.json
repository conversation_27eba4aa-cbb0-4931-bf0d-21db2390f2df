{"ConfigVersion": 11, "Checksum": "da96ad3b3730500d56c8e13d164d44a78eb6f062516717d4c4195f7995a8c391", "DEVICE": [{"DeviceType": "HAILO8L", "RuntimeAgent": "HAILORT", "SupportedDeviceTypes": "HAILORT/HAILO8L, HAILORT/HAILO8"}], "PRE_PROCESS": [{"InputN": 1, "InputH": 640, "InputW": 640, "InputC": 3, "InputQuantEn": true}], "MODEL_PARAMETERS": [{"ModelPath": "yolov8n_coco--640x640_quant_hailort_multidevice_1.hef"}], "POST_PROCESS": [{"OutputPostprocessType": "DetectionYoloHailo", "OutputNumClasses": 80, "LabelsPath": "labels_yolov8n_coco.json"}]}