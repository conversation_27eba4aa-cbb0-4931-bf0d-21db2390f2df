import cv2
import sys
import os

# use opencv to record a video from cam 1
def record_video(output_file, duration, fps=30.0, exposure=-7):
    print("waiting for open videocapture")
    cap = cv2.VideoCapture(1, cv2.CAP_DSHOW)
    if not cap.isOpened():
        print("Error: Could not open camera.")
        return
    print("open videocapture")
    cap.set(cv2.CAP_PROP_FRAME_WIDTH, 1920)
    cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 1080)
    cap.set(cv2.CAP_PROP_BUFFERSIZE, 3)
    cap.set(cv2.CAP_PROP_FPS, fps)
    cap.set(cv2.CAP_PROP_FOURCC, cv2.VideoWriter_fourcc(*'MJPG'))

    cap.set(cv2.CAP_PROP_AUTO_EXPOSURE, 0.25)
    cap.set(cv2.CAP_PROP_EXPOSURE, exposure) 


    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    out = cv2.VideoWriter(output_file, fourcc, fps, (int(cap.get(3)), int(cap.get(4))))

    frame_count = int(duration * fps)
    for _ in range(frame_count):
        ret, frame = cap.read()
        if not ret:
            print("Error: Failed to capture image.")
            break
        out.write(frame)
        cv2.imshow('Recording', frame)
        key = cv2.waitKey(1) & 0xFF
        if key == ord('q'):
            break
        elif key == ord("m"):
            # create a new output file with incremented filename
            base_name = output_file.rsplit('.', 1)[0]
            extension = output_file.rsplit('.', 1)[1]
            counter = 1
            while True:
                new_output_file = f"{base_name}_{counter}.{extension}"
                if not os.path.exists(new_output_file):
                    output_file = new_output_file
                    break
                counter += 1
            print("new outfile", output_file)
            out = cv2.VideoWriter(output_file, fourcc, fps, (int(cap.get(3)), int(cap.get(4))))

    cap.release()
    out.release()
    cv2.destroyAllWindows()

if __name__ == "__main__":
    # read an arg as output filename
    
    if len(sys.argv) > 1:
        output_file = sys.argv[1]
    else:
        output_file = 'output.mp4'
    record_video(output_file, 1000)  # Record for 10 seconds