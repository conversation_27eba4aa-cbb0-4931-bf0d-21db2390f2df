{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## Label analysis\n", "This notebook is for analysing the correcness of the (automat or manual) labels from the label studio. It works by comparing the filepath (which contains a folder with the correct object) to the annotation.   \n", "It shows ids for annotations with wrong annotations or multiple annotations."]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import json"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["with open(\"./project-6-at-2025-04-06-13-25-d5ac8fce.json\", \"r\") as f:\n", "    label_annotations = json.load(f)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["labels = {0: 'bacon',\n", " 1: 'carrot',\n", " 2: 'cucumber',\n", " 3: 'egg',\n", " 4: 'eggs',\n", " 5: 'ketchup',\n", " 6: 'mayonnaise',\n", " 7: 'mustard',\n", " 8: 'yogurt'}"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Number of entries with multiple annotations: 0\n"]}, {"data": {"text/plain": ["[]"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["# get all entries with multiple annotations\n", "multiple_annotations = []\n", "for entry in label_annotations:\n", "    if entry['annotations'][0] and len(entry['annotations'][0]['result']) > 1:\n", "        multiple_annotations.append(entry['id'])\n", "print(f\"Number of entries with multiple annotations: {len(multiple_annotations)}\")\n", "multiple_annotations"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["wrongs = {label: [] for label in labels.values()}\n", "for label in labels.values():\n", "    for x in [x for x in label_annotations if label+\"/\" in x['data']['image']]:\n", "        if not x['annotations']:\n", "            continue\n", "        if not x['annotations'][0]['result']:\n", "            continue\n", "        if x['annotations'][0]['result'][0]['value']['rectanglelabels'][0] != label:\n", "            wrongs[label].append(x)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/plain": ["({'bacon': 0,\n", "  'carrot': 0,\n", "  'cucumber': 0,\n", "  'egg': 0,\n", "  'eggs': 0,\n", "  'ketchup': 0,\n", "  'mayonnaise': 0,\n", "  'mustard': 0,\n", "  'yogurt': 0},\n", " 0)"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["{label: len(wrongs[label]) for label in wrongs},  sum([len(wrongs[label]) for label in wrongs]) # images with wrong labels"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'bacon': [],\n", " 'carrot': [],\n", " 'cucumber': [],\n", " 'egg': [],\n", " 'eggs': [],\n", " 'ketchup': [],\n", " 'mayonnaise': [],\n", " 'mustard': [],\n", " 'yogurt': []}"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["# ids of the wrong labels\n", "wrong_labels_ids = {label: [x['id'] for x in wrongs[label]] for label in wrongs}\n", "wrong_labels_ids"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["mult_labels = {label: [] for label in labels.values()}\n", "wrong_counter = 0\n", "for label in labels.values():\n", "    for x in [x for x in label_annotations if label+\"/\" in x['data']['image']]:\n", "        if not x['annotations']:\n", "            continue\n", "        if not x['annotations'][0]['result']:\n", "            continue\n", "        if len(x['annotations'][0]['result']) > 1:\n", "            #with open(\"test.json\", \"w\") as f:\n", "            #    json.dump(x, f)\n", "            mult_labels[label].append(x)\n", "            for result in x['annotations'][0]['result']:\n", "                if result['value']['rectanglelabels'][0] != label:\n", "                    wrong_counter += 1\n", "                    break\n", "        ##if x['annotations'][0]['result'][0]['value']['rectanglelabels'][0] != label:\n", "        # #   mult_labels[label].append(x)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/plain": ["({'bacon': 0,\n", "  'carrot': 0,\n", "  'cucumber': 0,\n", "  'egg': 0,\n", "  'eggs': 0,\n", "  'ketchup': 0,\n", "  'mayonnaise': 0,\n", "  'mustard': 0,\n", "  'yogurt': 0},\n", " 0,\n", " 0)"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["{label: len(mult_labels[label]) for label in mult_labels}, sum([len(mult_labels[label]) for label in mult_labels]), wrong_counter # images with multiple labels, wrong_counter: images with at least one of multiple labels wrong"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'bacon': [],\n", " 'carrot': [],\n", " 'cucumber': [],\n", " 'egg': [],\n", " 'eggs': [],\n", " 'ketchup': [],\n", " 'mayonnaise': [],\n", " 'mustard': [],\n", " 'yogurt': []}"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["# ids of the multilabels\n", "mult_labels_ids = {label: [x['id'] for x in mult_labels[label]] for label in mult_labels}\n", "mult_labels_ids"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'bacon': [],\n", " 'carrot': [],\n", " 'cucumber': [],\n", " 'egg': [],\n", " 'eggs': [],\n", " 'ketchup': [],\n", " 'mayonnaise': [],\n", " 'mustard': [],\n", " 'yogurt': []}"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["# which ids are in mutl_labels_id and wrong_labels_id\n", "duplicate_codes = {k:[id for id in mult_labels_ids[k] if id in wrong_labels_ids[k]] for k in mult_labels_ids}\n", "duplicate_codes\n", "\n", "#eindeutig_multi_label_ids = {k: [id for id ]}\n", "# duplicates are cleaned"]}], "metadata": {"kernelspec": {"display_name": "kuehl_train", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 2}