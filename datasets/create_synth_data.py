from enum import Enum
import os
import random
import math
from PIL import Image, ImageEnhance, ImageFilter
from tqdm import tqdm

class AugmentationType(Enum):
    SIZE = 0
    ROTATION = 1
    OCCLUSION = 2
    BRIGHTNESS = 3
    GRAYSCALE = 4
    BLUR = 5
    HUE = 6
    MULTI = 7

augmentation_config = {
    AugmentationType.SIZE: (0.05, 0.20), # percentage of background image size
    AugmentationType.ROTATION: (-180, 180),  # degrees
    AugmentationType.OCCLUSION: (0, 0.75),  # percentage of occlusion
    AugmentationType.BRIGHTNESS: (0.35, 1.5),
    AugmentationType.BLUR: (0, 5),  # Gaussian blur radius
    AugmentationType.GRAYSCALE: (0.1, 0.1),  # percentage of grayscale
    AugmentationType.HUE: (-0.05, 0.05),  # hue shift
    AugmentationType.MULTI: (1, 5)  # number of objects per image
}

class ImageClassPair:
    """A class to hold image paths and their corresponding class IDs."""
    
    def __init__(self, image_path: str, class_id: int):
        self.image_path = image_path
        self.class_id = class_id

images_with_ids = [
    ImageClassPair("obj_images/bacon_crop.png", 0),
    ImageClassPair("obj_images/carrot_crop.png", 1),
    ImageClassPair("obj_images/cucumber_crop.png", 2),
    ImageClassPair("obj_images/egg_crop.png", 3),
    ImageClassPair("obj_images/eggs_crop.png", 4),
    ImageClassPair("obj_images/ketchup_crop.png", 5),
    ImageClassPair("obj_images/mayo_crop.png", 6),
    ImageClassPair("obj_images/mustard_crop.png", 7),
    ImageClassPair("obj_images/yogurt_crop.png", 8)
]

background_imgs = ["background720.png", "background720_2.png", "background720_3.png", "background720_4.png"]
occlusion_img = "occlusion_img_crop.png"

# --- Main script ---

NUM_IMAGES = 1905
OUTPUT_DIR = "synth2_data"
IMG_DIR = os.path.join(OUTPUT_DIR, "images")
LABEL_DIR = os.path.join(OUTPUT_DIR, "labels")

def apply_hue(image, factor):
    """Applies a hue shift to the image."""
    if factor == 0:
        return image
    
    hsv_image = image.convert('HSV')
    h, s, v = hsv_image.split()
    
    # Apply hue shift. Hue is in [0, 255] for PIL.
    # factor is in [-0.5, 0.5]. We map it to a shift.
    # A factor of 0.05 means a 0.05 * 360 = 18 degree shift.
    # In PIL terms, 0.05 * 255.
    hue_shift = int(factor * 255)
    
    h = h.point(lambda i: (i + hue_shift) % 256)
    
    final_hsv = Image.merge('HSV', (h, s, v))
    return final_hsv.convert('RGB')

def create_synthetic_data():
    """Generates synthetic data and saves images and labels."""
    os.makedirs(IMG_DIR, exist_ok=True)
    os.makedirs(LABEL_DIR, exist_ok=True)

    start_index = 0
    if os.path.exists(IMG_DIR):
        existing_files = [f for f in os.listdir(IMG_DIR) if f.endswith('.png')]
        if existing_files:
            existing_indices = [int(f.split('.')[0]) for f in existing_files if f.split('.')[0].isdigit()]
            if existing_indices:
                start_index = max(existing_indices) + 1

    if start_index >= NUM_IMAGES:
        print(f"Found {start_index} images, which is >= total desired ({NUM_IMAGES}). No new images will be generated.")
        return

    print(f"Starting image generation from index {start_index}.")

    for img_counter in tqdm(range(start_index, NUM_IMAGES)):
        # This print statement is now redundant with tqdm
        # if (img_counter + 1) % 50 == 0:
        #     print(f"Generating image {img_counter + 1}/{NUM_IMAGES}...")

        # 1. Load background
        bg_path = random.choice(background_imgs)
        background = Image.open(bg_path).convert("RGBA")
        bg_w, bg_h = background.size
        
        composed_img = background.copy()
        labels = []

        num_objects = random.randint(*augmentation_config[AugmentationType.MULTI])
        
        for _ in range(num_objects):
            # Choose a random object
            item = random.choice(images_with_ids)
            fg_img_original = Image.open(item.image_path).convert("RGBA")

            # 2. Augment foreground (size, rotation)
            size_factor = random.uniform(*augmentation_config[AugmentationType.SIZE])
            bg_longer_side = max(bg_w, bg_h)
            
            fg_w_orig, fg_h_orig = fg_img_original.size
            aspect_ratio = fg_h_orig / fg_w_orig

            if fg_w_orig >= fg_h_orig:
                # Landscape or square
                new_fg_w = int(bg_longer_side * size_factor)
                new_fg_h = int(new_fg_w * aspect_ratio)
            else:
                # Portrait
                new_fg_h = int(bg_longer_side * size_factor)
                new_fg_w = int(new_fg_h / aspect_ratio)
            
            if new_fg_w == 0 or new_fg_h == 0: continue

            fg_resized = fg_img_original.resize((new_fg_w, new_fg_h), Image.Resampling.LANCZOS)

            rotation_angle = random.uniform(*augmentation_config[AugmentationType.ROTATION])
            fg_rotated = fg_resized.rotate(rotation_angle, expand=True, resample=Image.Resampling.BICUBIC)
            
            fg_final = fg_rotated
            fg_w, fg_h = fg_final.size

            # 3. Place foreground on background
            max_x = bg_w - fg_w
            max_y = bg_h - fg_h
            if max_x < 0 or max_y < 0:
                continue

            paste_x = random.randint(0, max_x)
            paste_y = random.randint(0, max_y)
            
            composed_img.paste(fg_final, (paste_x, paste_y), fg_final)

            # YOLO format: class_id x_center y_center width height (normalized)
            yolo_x = (paste_x + fg_w / 2) / bg_w
            yolo_y = (paste_y + fg_h / 2) / bg_h
            yolo_w = fg_w / bg_w
            yolo_h = fg_h / bg_h
            labels.append(f"{item.class_id} {yolo_x} {yolo_y} {yolo_w} {yolo_h}")

            # 4. Occlusion
            # Give a 75% chance to apply occlusion to an object
            if random.random() < 0.75:
                occlusion_percentage = random.uniform(*augmentation_config[AugmentationType.OCCLUSION])
                if occlusion_percentage > 0:
                    occ_img_original = Image.open(occlusion_img).convert("RGBA")
                    
                    # Scale occlusion image
                    occ_area = fg_w * fg_h * occlusion_percentage
                    occ_aspect_ratio = occ_img_original.height / occ_img_original.width
                    new_occ_w = int(math.sqrt(occ_area / occ_aspect_ratio))
                    new_occ_h = int(new_occ_w * occ_aspect_ratio)

                    if new_occ_w > 0 and new_occ_h > 0:
                        occ_resized = occ_img_original.resize((new_occ_w, new_occ_h), Image.Resampling.LANCZOS)
                        
                        # Rotate occlusion image
                        occlusion_rotation_angle = random.uniform(*augmentation_config[AugmentationType.ROTATION])
                        occ_final = occ_resized.rotate(occlusion_rotation_angle, expand=True, resample=Image.Resampling.BICUBIC)

                        # Place occlusion image over the foreground object
                        occ_w, occ_h = occ_final.size
                        
                        # Check if placement range is valid to prevent ValueError
                        rand_x_min = -occ_w // 4
                        rand_x_max = fg_w - 3 * occ_w // 4
                        rand_y_min = -occ_h // 4
                        rand_y_max = fg_h - 3 * occ_h // 4

                        if rand_x_min <= rand_x_max and rand_y_min <= rand_y_max:
                            occ_paste_x = paste_x + random.randint(rand_x_min, rand_x_max)
                            occ_paste_y = paste_y + random.randint(rand_y_min, rand_y_max)
                            
                            composed_img.paste(occ_final, (occ_paste_x, occ_paste_y), occ_final)

        # 5. Final image augmentations
        final_img = composed_img.convert("RGB")

        # Brightness
        brightness = random.uniform(*augmentation_config[AugmentationType.BRIGHTNESS])
        enhancer = ImageEnhance.Brightness(final_img)
        final_img = enhancer.enhance(brightness)

        # Blur
        blur_radius = random.uniform(*augmentation_config[AugmentationType.BLUR])
        if blur_radius > 0:
            final_img = final_img.filter(ImageFilter.GaussianBlur(radius=blur_radius))

        # Grayscale
        if random.random() < augmentation_config[AugmentationType.GRAYSCALE][0]:
             enhancer = ImageEnhance.Color(final_img)
             final_img = enhancer.enhance(0.0) # 0.0 for grayscale

        # Hue
        hue_factor = random.uniform(*augmentation_config[AugmentationType.HUE])
        final_img = apply_hue(final_img, hue_factor)

        # 6. Save image and YOLO label
        if not labels:
            continue # Don't save images with no objects

        img_filename = f"{img_counter}.png"
        label_filename = f"{img_counter}.txt"

        final_img.save(os.path.join(IMG_DIR, img_filename))

        with open(os.path.join(LABEL_DIR, label_filename), "w") as f:
            f.write("\n".join(labels))

    print(f"Finished generating {NUM_IMAGES} images.")

if __name__ == "__main__":
    create_synthetic_data()