{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## Syntetic data generation\n", "\n", "This notebook generates syntetic data given a background image and a foreground image.\n", "\n", "The foreground image (object image) should be cropped to the object and contain transparent background.\n", "\n", "Different object augmentations are used:\n", "- size\n", "- rotation\n", "- occlusion (use image of hand to overlay on top of the object)\n", "- image hue\n", "- lightness/brightness\n", "- rgb -> grayscale\n", "\n", "Output format is the yolo format:\n", "- folder \"image\" contains xyz.png\n", "- folder \"labels\" contains xyz.txt\n", "labels are matched to images by name and the label format is: `class x_center y_center width height` per line"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "from enum import Enum\n", "import random\n", "from PIL import Image\n", "from PIL import ImageEnhance\n", "from PIL import ImageFilter\n", "import matplotlib.pyplot as plt\n", "from matplotlib.offsetbox import OffsetImage, AnnotationBbox\n", "from tqdm import tqdm\n", "\n", "class AugmentationType(Enum):\n", "    SIZE = 0\n", "    ROTATION = 1\n", "    OCCLUSION = 2\n", "    BRIGHTNESS = 3\n", "    GRAYSCALE = 4\n", "    BLUR = 5\n", "    HUE = 6\n", "\n", "augmentation_config = {\n", "    AugmentationType.SIZE: (0.1, 0.3), # percentage of background image size\n", "    AugmentationType.ROTATION: (-180, 180),  # degrees\n", "    AugmentationType.OCCLUSION: (0, 0.75),  # percentage of occlusion\n", "    AugmentationType.BRIGHTNESS: (0.35, 1.5),\n", "    AugmentationType.BLUR: (0, 5),  # Gaussian blur radius\n", "    AugmentationType.GRAYSCALE: (1, 1),  # percentage of grayscale\n", "    AugmentationType.HUE: (-0.05, 0.05)  # hue shift\n", "}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def augment_image(img: Image.Image, aug: AugmentationType, background_img: Image.Image, occlusion_img: Image.Image, value: float = None) -> tuple[Image.Image, tuple[float]]:\n", "    \"\"\"Augment the image based on the specified augmentation type.\n", "    \n", "    Size should be applied first.\n", "    Rotation should be applied after size, in order to get the correct output size.\n", "\n", "    Args:\n", "        img (Image.Image): The image to augment.\n", "        aug (AugmentationType): The augmentation type to apply.\n", "        background_img (Image.Image): The background image to use for SIZE augmentation.\n", "        occlusion_img (Image.Image): The occlusion image to use for OCCLUSION augmentation.\n", "        value (float): The value to use for the augmentation (if None a random value from within augmentation_config is used).\n", "    \n", "    Returns:\n", "        Image.Image: The augmented image.\n", "        tuple[float]: A tuple containing the x center, y center, width, and height of the augmented object inside the returned image.\n", "\n", "    \"\"\"\n", "    # if no value is given, pick a random value from the range defined in augmentation_config\n", "    if value is None:\n", "        value = random.uniform(augmentation_config[aug][0], augmentation_config[aug][1])\n", "\n", "    x_center, y_center, img_width, img_height = -1, -1, -1, -1\n", "\n", "    match aug:\n", "        case AugmentationType.SIZE:\n", "            if background_img is None:\n", "                raise ValueError(\"Background image width is not set for SIZE augmentation.\")\n", "\n", "            if img.width > img.height:\n", "                width = int(value * background_img.width)\n", "                height = int(width * (img.height / img.width))\n", "            else:\n", "                height = int(value * background_img.height)\n", "                width = int(height * (img.width / img.height))\n", "            # resize foreground image\n", "            img = img.resize((width, height))\n", "        case AugmentationType.ROTATION:\n", "            # rotate foreground image\n", "            img = img.rotate(value, expand=1, fillcolor=(0, 0, 0, 0))\n", "        case AugmentationType.OCCLUSION:\n", "            # apply occlusion to foreground image\n", "            if occlusion_img is None:\n", "                raise ValueError(\"Occlusion image width is not set for OCCLUSION augmentation.\")\n", "\n", "            # Randomly resize the occlusion image using the SIZE augmentation config \n", "            occlusion_size = random.uniform(augmentation_config[AugmentationType.SIZE][0], augmentation_config[AugmentationType.SIZE][1])\n", "            if occlusion_img.width > occlusion_img.height:\n", "                occlusion_width = int(occlusion_size * background_img.width)\n", "                occlusion_height = int(occlusion_width * (occlusion_img.height / occlusion_img.width))\n", "            else:\n", "                occlusion_height = int(occlusion_size * background_img.height)\n", "                occlusion_width = int(occlusion_height * (occlusion_img.width / occlusion_img.height))\n", "            occlusion_img = occlusion_img.resize((occlusion_width, occlusion_height))\n", "\n", "            # generate a random x position always at least half overlapping the image\n", "            rand_x = random.randint(-occlusion_width // 2, img.width - occlusion_width // 2)\n", "\n", "            # calculate the x overlap of the occlusion image with the foreground image\n", "            if rand_x < 0:\n", "                overlap_x = min(rand_x + occlusion_width, img.width)\n", "            else:\n", "                overlap_x = min(img.width - rand_x, img.width)\n", "\n", "            # check if the occlusion image is too small to overlap the foreground image\n", "            target_overlap_area = value * (img.width * img.height)\n", "            occlusion_area = occlusion_width * occlusion_height\n", "            if occlusion_area < target_overlap_area:\n", "                # occlusion image cant cover all of the target area, just paste it anywhere on top of it\n", "                y = random.randint(0, abs(img.height - occlusion_height))\n", "            else:\n", "                overlap_y = target_overlap_area / overlap_x\n", "                y = img.height - overlap_y\n", "\n", "            img_width = img.width\n", "            img_height = img.height\n", "\n", "            # Place the occlusion image at the calculated position (rand_x, y)\n", "            # Extend the foreground image if the occlusion image goes out of bounds\n", "            if rand_x < 0 or y < 0 or rand_x + occlusion_width > img.width or y + occlusion_height > img.height:\n", "                new_width = int(max(img.width, rand_x + occlusion_width, 0) - min(0, rand_x))\n", "                new_height = int(max(img.height, y + occlusion_height, 0) - min(0, int(y)))\n", "                extended_img = Image.new(\"RGBA\", (new_width, new_height), (0, 0, 0, 0))\n", "                offset_x = -min(0, rand_x)\n", "                offset_y = -min(0, int(y))\n", "                extended_img.paste(img, (offset_x, offset_y))\n", "            else:\n", "                extended_img = img\n", "                offset_x = 0\n", "                offset_y = 0\n", "            # Paste the occlusion image onto the foreground image\n", "            extended_img.paste(occlusion_img, (max(0, rand_x) + offset_x, max(0, int(y)) + offset_y), occlusion_img)\n", "\n", "            x_center = (img.width / 2) + offset_x\n", "            y_center = (img.height / 2) + offset_y\n", "            img_width = img.width\n", "            img_height = img.height\n", "            img = extended_img\n", "        case AugmentationType.BRIGHTNESS:\n", "            # apply brightness to foreground image\n", "            enhancer = ImageEnhance.Brightness(img)\n", "            img = enhancer.enhance(value)\n", "        case AugmentationType.GRAYSCALE:\n", "            # convert to grayscale\n", "            alpha = img.getchannel(\"A\")  # Preserve the alpha channel\n", "            img = img.convert(\"L\")  # Convert to grayscale\n", "            img = Image.merge(\"RGBA\", (img, img, img, alpha))  # Add the alpha channel back\n", "        case AugmentationType.BLUR:\n", "            # apply Gaussian blur to foreground image\n", "            img = img.filter(ImageFilter.GaussianBlur(value))\n", "        case AugmentationType.HUE:\n", "            r, g, b, a = img.split() # split channels\n", "            img = Image.merge(\"RGB\", (r, g, b)).convert(\"HSV\")\n", "            h, s, v = img.split()\n", "            # apply hue shift\n", "            h = h.point(lambda p: (p + int(value * 255)) % 256)\n", "            # merge channels back and add back the alpha channel\n", "            img = Image.merge(\"HSV\", (h, s, v)).convert(\"RGB\")\n", "            img = Image.merge(\"RGBA\", (*img.split(), a))\n", "\n", "    if x_center == -1:\n", "        x_center = (img.width / 2)\n", "        y_center = (img.height / 2)\n", "        img_width = img.width\n", "        img_height = img.height\n", "    return img, (x_center, y_center, img_width, img_height)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def test_augmentations(img, augmentation, background_img, occlusion_img):\n", "    \"\"\"Test the augmentations by applying them to the image and displaying the results in a 3x3 grid.\"\"\"\n", "    \n", "    # Create a 3x3 grid of subplots\n", "    fig, axs = plt.subplots(3, 3, figsize=(12, 12))\n", "\n", "    # Apply the specified augmentation to the image and display the results\n", "    aug_types = list(AugmentationType)  # Get all augmentation types\n", "    num_augmentations = len(aug_types)\n", "    for i in range(3):\n", "        for j in range(3):\n", "            ax = axs[i, j]\n", "            index = i * 3 + j\n", "            if index == 0:\n", "                ax.set_title(\"Original\")\n", "                augmented_img = img.copy()\n", "            elif index - 1 < num_augmentations:\n", "                aug = aug_types[index - 1]  # Skip the original image\n", "                augmented_img, bbox = augment_image(img.copy(), aug, background_img, occlusion_img)\n", "                ax.set_title(aug.name)\n", "            else:\n", "                ax.axis('off')  # Turn off unused subplots\n", "                continue\n", "            \n", "            # Display image at its native size using OffsetImage\n", "            imagebox = OffsetImage(augmented_img, zoom=min(170 / augmented_img.width, 170 / augmented_img.height))  # Ensure max width and height are 170 px\n", "            ab = AnnotationBbox(imagebox, (0.5, 0.5), frameon=False, box_alignment=(0.5, 0.5))\n", "            ax.add_artist(ab)\n", "\n", "            ax.set_xlim(0, 1)\n", "            ax.set_ylim(0, 1)\n", "            ax.set_aspect('equal')\n", "            ax.axis('off')\n", "\n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "def test_augmentation(img, augmentation, background_img, occlusion_img, n, save:bool = True, display:bool = False):\n", "    \"\"\"Test a single augmentation by applying it to the image. \n", "    \n", "    from the range of valid values for the augmentation_type n*n-1 are used. \n", "    The result is displayed in a nxn grid.\"\"\"\n", "    \n", "\n", "    # Get the range of valid values for the specified augmentation\n", "    value_range = augmentation_config[augmentation]\n", "    step = (value_range[1] - value_range[0]) / (n * n - 2)\n", "    values = [value_range[0]] + [value_range[0] + i * step for i in range(n * n - 2)] + [value_range[1]]\n", "\n", "    # Create a nxn grid of subplots\n", "    fig, axs = plt.subplots(n, n, figsize=(12, 12))\n", "\n", "    # Apply the specified augmentation to the image and display the results\n", "    for i in range(n):\n", "        for j in range(n):\n", "            ax = axs[i, j]\n", "            index = i * n + j\n", "            if index == 0:\n", "                ax.set_title(\"Original \" + augmentation.name)\n", "                augmented_img = img.copy()\n", "            else:\n", "                ax = axs[i, j]\n", "                value = values[i * n + j]\n", "                augmented_img, bbox = augment_image(img.copy(), augmentation, background_img, occlusion_img, value)\n", "                ax.set_title(f\"Value: {value:.2f}\")\n", "                ax.axis('off')\n", "            \n", "            # Display image at its native size using OffsetImage\n", "            imagebox = OffsetImage(augmented_img, zoom=min(170 / augmented_img.width, 170 / augmented_img.height))  # Ensure max width and height are 170 px\n", "            ab = AnnotationBbox(imagebox, (0.5, 0.5), frameon=False, box_alignment=(0.5, 0.5))\n", "            ax.add_artist(ab)\n", "\n", "            ax.set_xlim(0, 1)\n", "            ax.set_ylim(0, 1)\n", "            ax.set_aspect('equal')\n", "            ax.axis('off')\n", "\n", "    plt.tight_layout()\n", "    if save:\n", "        plt.savefig(f\"out_img/test_augmentation_{augmentation.name}.png\")\n", "    if not display:\n", "        plt.close(fig)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# test a single augmentation\n", "img = Image.open(\"obj_images/carrot_crop.png\").convert(\"RGBA\")\n", "back_img = Image.open(\"background720.png\").convert(\"RGBA\")\n", "occlusion_img = Image.open(\"occlusion_img_crop.png\").convert(\"RGBA\")\n", "test_augmentation(img, AugmentationType.OCCLUSION, back_img, occlusion_img, 3, save=False, display=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# test all augmentations and store the example images for the full value ranges\n", "for aug in AugmentationType:\n", "    img = Image.open(\"obj_images/carrot_crop.png\").convert(\"RGBA\")\n", "    back_img = Image.open(\"background720.png\").convert(\"RGBA\")\n", "    occlusion_img = Image.open(\"occlusion_img_crop.png\").convert(\"RGBA\")\n", "    test_augmentation(img, aug, back_img, occlusion_img, 3, save=True, display=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["img = Image.open(\"obj_images/carrot_crop.png\").convert(\"RGBA\")\n", "back_img = Image.open(\"background720.png\").convert(\"RGBA\")\n", "occlusion_img = Image.open(\"occlusion_img_crop.png\").convert(\"RGBA\")\n", "test_augmentations(img, AugmentationType.SIZE, back_img, occlusion_img)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class ImageClassPair:\n", "    \"\"\"A class to hold image paths and their corresponding class IDs.\"\"\"\n", "    \n", "    def __init__(self, image_path: str, class_id: int):\n", "        self.image_path = image_path\n", "        self.class_id = class_id\n", "\n", "def prepare_augmentations_order(augmentations: list[AugmentationType]) -> list[AugmentationType]:\n", "    \"\"\"Prepare the order of augmentations to apply.\n", "    \n", "    The order is important, e.g. SIZE should be applied first, ROTATION after SIZ<PERSON>, and OCCLUSION last.\n", "    \n", "    Args:\n", "        augmentations (list[AugmentationType]): List of augmentations to apply.\n", "    \n", "    Returns:\n", "        list[AugmentationType]: Ordered list of augmentations.\n", "    \"\"\"\n", "    # if rotation augmentation is used, it should be applied after size augmentation\n", "    if AugmentationType.ROTATION in augmentations:\n", "        augmentations.remove(AugmentationType.ROTATION)\n", "        augmentations.append(AugmentationType.ROTATION)\n", "    # if occlusion augmentation is used, it should be applied as last augmentatino\n", "    if AugmentationType.OCCLUSION in augmentations:\n", "        augmentations.remove(AugmentationType.OCCLUSION)\n", "        augmentations.append(AugmentationType.OCCLUSION)\n", "    return augmentations\n", "\n", "def synthesize_data(background_imgs: list[str]|str,\n", "                    foreground_imgs: list[ImageClassPair],\n", "                    occlusion_img: str,\n", "                    augmentations: list[AugmentationType],\n", "                    n: int,\n", "                    output_dir: str,\n", "                    aug_selection_fct = None) -> list[tuple[str,str]]:\n", "    \"\"\"Create synthesized data from given images and configurations.\n", "\n", "    Overwrites files in the output directory if they already exist.\n", "    \n", "    Args:\n", "        background_imgs: list[str]|str: List of background image paths, from which is sampled or single path.\n", "        foreground_imgs: list[ImageClassPair]: List of ImageClassPair objects, each containing the path to the image and its class ID. A random number of these images is sampled for each generated image.\n", "        occlusion_img: str: The path to the occlusion image. Must be cropped to the object and have transparent background.\n", "        augmentations: list[AugmentationType]: List of augmentations to apply to the object image.\n", "        n: int: The number of image-label-pairs to generate.\n", "        output_dir: str: The path to the output directory, in which the folders `images` and `labels` are created if not existant.\n", "        aug_selection_fct: function: A function to randomly select augmentations from a list of augmentations. If None, all augmentations are applied. Is called for each image/object on the image.\n", "    \n", "    Returns:\n", "        List of all the generated filenames of (image,label).\n", "\n", "    \"\"\"\n", "    # load the images\n", "    object_imgs = [Image.open(fg.image_path).convert(\"RGBA\") for fg in foreground_imgs]\n", "    class_ids = [fg.class_id for fg in foreground_imgs]\n", "    #foreground_img: Image.Image = Image.open(foreground_img).convert(\"RGBA\")\n", "    if type(background_imgs) == str:\n", "        background_imgs = [background_imgs]\n", "    \n", "    occlusion_img: Image.Image = Image.open(occlusion_img).convert(\"RGBA\")\n", "\n", "    label_dir = output_dir + \"/labels\"\n", "    if not os.path.exists(label_dir):\n", "        os.makedirs(label_dir)\n", "    image_dir = output_dir + \"/images\"\n", "    if not os.path.exists(image_dir):\n", "        os.makedirs(image_dir)\n", "\n", "    generations = []\n", "\n", "    label_count = len(os.listdir(label_dir))\n", "    img_count = len(os.listdir(image_dir))\n", "    \n", "    # generate n images:\n", "    for _ in tqdm(range(n)):\n", "        # augment foreground image\n", "        background_img: Image.Image = Image.open(random.choice(background_imgs)).convert(\"RGBA\")\n", "        #fore_img = foreground_img.copy()\n", "        # get random number of foreground images from the list (1 - len(foreground_imgs))\n", "        sampled = random.sample(list(zip(object_imgs, class_ids)), random.randint(1, min(len(object_imgs),4)))\n", "        sampled_fore_imgs, sampled_class_ids = zip(*sampled)\n", "        sampled_fore_imgs = list(sampled_fore_imgs)\n", "        sampled_class_ids = list(sampled_class_ids)\n", "        #print(f\"Sampled {len(sampled_fore_imgs)} foreground images with class ids: {sampled_class_ids} from {len(object_imgs)} available foreground images.\")\n", "\n", "        locs = None\n", "        \n", "\n", "        locs_list = []\n", "        xys_list = []\n", "        augmented_img_list = []\n", "        for fore_img in sampled_fore_imgs:\n", "            # sample augmentations for each of the objects\n", "            select_augmentations = []\n", "            if aug_selection_fct is not None:\n", "                select_augmentations = aug_selection_fct(augmentations)\n", "            else:\n", "                select_augmentations = augmentations\n", "            select_augmentations = prepare_augmentations_order(select_augmentations)\n", "\n", "            for aug in select_augmentations:\n", "                fore_img, locs = augment_image(fore_img, aug, background_img, occlusion_img)\n", "                #print(f\"aug: {aug}, locs: {locs}, fore_img size: {fore_img.size}, background_img size: {background_img.size}\")\n", "            augmented_img_list.append(fore_img)\n", "            # add foreground image onto the background image (get x,y coordinates for the foreground image)\n", "            x = random.randint(0, background_img.width - fore_img.width)\n", "            y = random.randint(0, background_img.height - fore_img.height)\n", "            locs_list.append(locs)\n", "            xys_list.append((x, y))\n", "\n", "        # create the label file\n", "        # get file count in the label directory to create a unique filename\n", "        label_count += 1\n", "        label_filepath =  f\"{label_dir}/{label_count}.txt\"\n", "        with open(label_filepath, \"w\") as label_file:\n", "            # write the class id and the coordinates of the bounding box in yolo format\n", "            # calculate normalized coordinates for the bounding box\n", "            #x_center = (x + fore_img.width / 2) / background_img.width\n", "            #y_center = (y + fore_img.height / 2) / background_img.height\n", "            #width = fore_img.width / background_img.width\n", "            #height = fore_img.height / background_img.height\n", "            # use the coordinates from the augmentation instead of the ones from the background image\n", "            for i, (locs, xy) in enumerate(zip(locs_list, xys_list)):\n", "                x_center = (locs[0]+xy[0]) / background_img.width\n", "                y_center = (locs[1]+xy[1]) / background_img.height\n", "                width = locs[2] / background_img.width\n", "                height = locs[3] / background_img.height\n", "            \n", "                class_id = sampled_class_ids[i]\n", "                # write the class id and the normalized coordinates of the bounding box in YOLO format\n", "                assert width > 0 and height > 0, f\"Width or height is zero: {width}, {height}\"\n", "                label_file.write(f\"{class_id} {x_center} {y_center} {width} {height}\\n\")\n", "        \n", "        # paste the foreground image onto the background image\n", "        img = background_img.copy()\n", "        for fore_img, (x, y) in zip(augmented_img_list, xys_list):\n", "            img.paste(fore_img, (x, y), fore_img)\n", "        img = img.convert(\"RGB\")\n", "        # create the image file\n", "        img_count += 1\n", "        img_filepath = f\"{image_dir}/{img_count}.jpg\"\n", "        img.save(img_filepath)\n", "        generations.append((img_filepath, label_filepath))\n", "    return generations"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import random\n", "\n", "images_with_ids = [\n", "    ImageClassPair(\"obj_images/bacon_crop.png\", 0),\n", "    ImageClassPair(\"obj_images/carrot_crop.png\", 1),\n", "    ImageClassPair(\"obj_images/cucumber_crop.png\", 2),\n", "    ImageClassPair(\"obj_images/egg_crop.png\", 3),\n", "    ImageClassPair(\"obj_images/eggs_crop.png\", 4),\n", "    ImageClassPair(\"obj_images/ketchup_crop.png\", 5),\n", "    ImageClassPair(\"obj_images/mayo_crop.png\", 6),\n", "    ImageClassPair(\"obj_images/mustard_crop.png\", 7),\n", "    ImageClassPair(\"obj_images/yogurt_crop.png\", 8)\n", "]\n", "\n", "background_imgs = [\"background720.png\", \"background720_2.png\", \"background720_3.png\"]\n", "occlusion_img = \"occlusion_img_crop.png\"\n", "\n", "augmentations_rgb = [\n", "    AugmentationType.SIZE,\n", "    AugmentationType.ROTATION,\n", "    AugmentationType.BRIGHTNESS,\n", "    AugmentationType.BLUR,\n", "    AugmentationType.HUE,\n", "    AugmentationType.OCCLUSION\n", "]\n", "\n", "augmentations_l = [\n", "    AugmentationType.SIZE,\n", "    AugmentationType.ROTATION,\n", "    AugmentationType.BRIGHTNESS,\n", "    AugmentationType.OCCLUSION,\n", "    AugmentationType.GRAYSCALE,\n", "    AugmentationType.BLUR,\n", "    AugmentationType.HUE\n", "]\n", "\n", "def random_augment_selection(input_list):\n", "    \"\"\"Randomly remove a few elements from the input list.\"\"\"\n", "    mandatory = [AugmentationType.SIZE] # those get inserted from left to right at index 0; SIZE needs to be the last in this list\n", "    rando = [element for element in input_list if random.choice([True, False])]\n", "    \n", "    for aug in mandatory:\n", "        if aug not in rando:\n", "            rando.insert(0, aug)\n", "    return rando\n", "\n", "\n", "percentage_grey = 0.1\n", "n = 10\n", "\n", "output_dir = \"synthesized_data_background_multi\"\n", "\n", "#for _ in range(n):\n", "# synthesize data for RGB images\n", "synthesize_data(background_imgs=background_imgs,\n", "                foreground_imgs=images_with_ids,\n", "                occlusion_img=occlusion_img,\n", "                augmentations=augmentations_rgb,\n", "                n=int(n-percentage_grey*n),\n", "                output_dir=output_dir,\n", "                aug_selection_fct=random_augment_selection)\n", "\n", "# synthesize data for grayscale images\n", "synthesize_data(background_imgs=background_imgs,\n", "                foreground_imgs=images_with_ids,\n", "                occlusion_img=occlusion_img,\n", "                augmentations=augmentations_l,\n", "                n=int(percentage_grey*n),\n", "                output_dir=output_dir,\n", "                aug_selection_fct=random_augment_selection)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Mix synthetic data into real data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "import shutil\n", "import random\n", "\n", "target_dir = \"synthesized_data_background_mix/\" # dir containing the real data (synth data will be copied to this dir)\n", "\n", "# count number of files in directory images/train ending with .png\n", "def count_files_in_directory(directory):\n", "    count = 0\n", "    for filename in os.listdir(directory):\n", "        if filename.endswith(\".png\"):\n", "            count += 1\n", "    return count\n", "\n", "train_files_count = count_files_in_directory(target_dir + \"images/train\")\n", "# calculate number of synthetic files so synthhetic is 30% of the total number of files\n", "synthetic_files_count = (train_files_count*0.3)/0.7\n", "\n", "\n", "# add syntehtic file to the dataset\n", "# from synthesized_data_occlusion/images/train to datasets/datasetrealsynth/images/train\n", "# sampled randomly \n", "def copy_random_files(src_dir, dest_dir, num_files):\n", "    # Get a list of all files in the source directory\n", "    all_files = [f for f in os.listdir(src_dir) if f.endswith('.jpg')]\n", "    \n", "    # Randomly select the specified number of files\n", "    selected_files = random.sample(all_files, num_files)\n", "    \n", "    # Copy each selected file to the destination directory\n", "    for file_name in selected_files:\n", "        src_file_path = os.path.join(src_dir, file_name)\n", "        dest_file_path = os.path.join(dest_dir, file_name)\n", "        shutil.copy(src_file_path, dest_file_path)\n", "\n", "        src_label_path = os.path.join(src_dir.replace(\"images\", \"labels\"), file_name.replace(\".jpg\", \".txt\"))\n", "        dest_label_path = os.path.join(dest_dir.replace(\"images\", \"labels\"), file_name.replace(\".jpg\", \".txt\"))\n", "        shutil.copy(src_label_path, dest_label_path)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["copy_random_files(\"synthesized_data_background/images\", \"synthesized_data_background_mix/images/train\", int(synthetic_files_count))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["augmentations = [AugmentationType.SIZE,\n", "                 AugmentationType.ROTATION,\n", "                 AugmentationType.OCCLUSION,\n", "                 AugmentationType.BRIGHTNESS,\n", "                 #AugmentationType.GRAYSCALE,\n", "                 AugmentationType.BLUR,\n", "                 AugmentationType.HUE\n", "                 ]  \n", "\n", "res = synthesize_data(\"background.png\", \"obj_images/bacon_crop.png\", \"occlusion_img_crop.png\", augmentations, 10, \"./out_imgocc\", 0, random_augment_selection)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# visualize the generated images, and draw the bounding boxes from the labels\n", "from PIL import ImageDraw\n", "for img_path, label_path in res:\n", "    # load the image\n", "    img = Image.open(img_path).convert(\"RGBA\")\n", "    # load the label\n", "    with open(label_path, \"r\") as label_file:\n", "        lines = label_file.readlines()\n", "        for line in lines:\n", "            class_id, x, y, w, h = map(float, line.split())\n", "            # draw the bounding box on the image\n", "            x1 = int((x - w / 2) * img.width)\n", "            y1 = int((y - h / 2) * img.height)\n", "            x2 = int((x + w / 2) * img.width)\n", "            y2 = int((y + h / 2) * img.height)\n", "            img_draw = ImageDraw.Draw(img)\n", "            img_draw.rectangle([x1, y1, x2, y2], outline=\"red\", width=3)\n", "\n", "    plt.imshow(img)\n", "    plt.axis('off')\n", "    plt.show()"]}], "metadata": {"kernelspec": {"display_name": "k<PERSON>hl", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.10"}}, "nbformat": 4, "nbformat_minor": 2}