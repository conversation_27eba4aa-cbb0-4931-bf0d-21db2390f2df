{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["#!pip install ultralytics"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["from ultralytics import YOLO\n", "import cv2\n", "\n", "import torch\n", "device = torch.device('cuda' if torch.cuda.is_available() else 'mps' if torch.backends.mps.is_available() else 'cpu')"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["# Load a model\n", "# list of pretrained yolo models here: https://docs.ultralytics.com/tasks/detect/#models\n", "#model = YOLO(\"yolo11s.pt\", verbose=False) # automatically downloads the pretrained yolo model\n", "model = YOLO(\"yolo_all_train.pt\", verbose=False)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/plain": ["{0: 'bacon',\n", " 1: 'carrot',\n", " 2: 'cucumber',\n", " 3: 'egg',\n", " 4: 'eggs',\n", " 5: 'ketchup',\n", " 6: 'mayonnaise',\n", " 7: 'mustard',\n", " 8: 'yogurt'}"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["# list of trained classes\n", "model.names"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["# create a list to limit preditiction to following classes or none\n", "# person 0, banana 46, apple 47, orange 49\n", "inference_classes = [46,47,49]\n", "inference_classes = None"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Prediction"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["emit Action.INSERT ketchup\n", "emit Action.INSERT mayonnaise\n", "emit Action.INSERT bacon\n", "emit Action.REMOVE ketchup\n", "emit Action.REMOVE ketchup\n", "emit Action.INSERT ketchup\n", "emit Action.REMOVE mayonnaise\n", "emit Action.INSERT mayonnaise\n", "emit Action.REMOVE ketchup\n", "emit Action.INSERT ketchup\n", "emit Action.REMOVE eggs\n", "emit Action.INSERT bacon\n", "emit Action.REMOVE bacon\n", "emit Action.REMOVE mayonnaise\n", "emit Action.REMOVE ketchup\n", "emit Action.REMOVE eggs\n", "emit Action.INSERT mayonnaise\n", "emit Action.REMOVE ketchup\n", "emit Action.REMOVE eggs\n", "emit Action.REMOVE mayonnaise\n", "emit Action.REMOVE ketchup\n", "emit Action.INSERT mayonnaise\n", "emit Action.INSERT mayonnaise\n", "emit Action.REMOVE mayonnaise\n", "emit Action.REMOVE ketchup\n", "emit Action.REMOVE mayonnaise\n", "emit Action.INSERT ketchup\n", "emit Action.REMOVE mayonnaise\n", "emit Action.REMOVE mayonnaise\n", "emit Action.REMOVE ketchup\n", "emit Action.INSERT ketchup\n", "emit Action.INSERT mayonnaise\n", "emit Action.REMOVE mayonnaise\n", "emit Action.REMOVE ketchup\n"]}, {"data": {"text/plain": ["-1"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["import detect_action\n", "import os\n", "\n", "vid_path = \"datasets/custom videos/source videos/1.mov\"\n", "vid_path = \"datasets/testrec.mp4\"\n", "cap = cv2.VideoCapture(vid_path)\n", "\n", "action_state = detect_action.State()\n", "\n", "while True:\n", "    success, img = cap.read()\n", "    if not success:\n", "        break\n", "    results = model.predict(img, conf=0.8, stream=True, classes=inference_classes, verbose=False, device=device)\n", "    results = list(results)\n", "\n", "    # detect action based on box\n", "    box, klass = None, -1\n", "    if len(results) > 0 and len(results[0].boxes) > 0:\n", "        box = results[0].boxes[0]\n", "        klass = int(box.cls)\n", "        box = box.xywhn[0]\n", "    detect_action.on_frame(action_state, box, klass)\n", "\n", "    # draw predictions on output visualization\n", "    for r in results:\n", "        boxes = r.boxes\n", "\n", "        for box in boxes:\n", "            # bounding box\n", "            x1, y1, x2, y2 = box.xyxy[0]\n", "            x1, y1, x2, y2 = int(x1), int(y1), int(x2), int(y2) # convert to int values\n", "\n", "            # draw bounding box on image\n", "            cv2.rectangle(img, (x1, y1), (x2, y2), (255, 0, 255), 3)\n", "\n", "            # draw object details on image\n", "            pred_text = f\"{r.names[int(box.cls)]}: {box.conf.item():.2f}\"\n", "            org = [x1, y2]\n", "            font = cv2.FONT_HERSHEY_SIMPLEX\n", "            fontScale = 1\n", "            color = (255, 0, 0)\n", "            thickness = 2\n", "\n", "            cv2.putText(img, pred_text, org, font, fontScale, color, thickness)\n", "\n", "    cv2.imshow('Output', img)\n", "    if cv2.wait<PERSON>ey(1) == ord('q'):\n", "        break\n", "\n", "cap.release()\n", "cv2.destroyAllWindows()\n", "cv2.<PERSON><PERSON><PERSON>(1)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Prediction with pause, next/prev frame"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# run inference on a video\n", "# p for pause, n for next frame, b for previous frame\n", "vid_path = \"datasets/testrec.mp4\"\n", "\n", "cap = cv2.VideoCapture(vid_path)\n", "\n", "paused = False\n", "frame_number = 0\n", "total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))\n", "annotated_frames = {}\n", "\n", "def get_frame(frame_number):\n", "    cap.set(cv2.CAP_PROP_POS_FRAMES, frame_number)\n", "    success, img = cap.read()\n", "    return success, img\n", "\n", "while True:\n", "    if not paused:\n", "        if frame_number >= total_frames:\n", "            break\n", "        success, img = get_frame(frame_number)\n", "        if not success:\n", "            break\n", "        frame_number += 1\n", "    else:\n", "        success, img = get_frame(frame_number-1)\n", "\n", "    if frame_number-1 not in annotated_frames:\n", "        results = model.predict(img, conf=0.8, stream=True, classes=inference_classes)\n", "\n", "        # coordinates\n", "        for r in results:\n", "            boxes = r.boxes\n", "\n", "            for box in boxes:\n", "                # bounding box\n", "                x1, y1, x2, y2 = box.xyxy[0]\n", "                x1, y1, x2, y2 = int(x1), int(y1), int(x2), int(y2) # convert to int values\n", "\n", "                # put box in cam\n", "                cv2.rectangle(img, (x1, y1), (x2, y2), (255, 0, 255), 3)\n", "\n", "                # object details\n", "                org = [x1, y2]\n", "                font = cv2.FONT_HERSHEY_SIMPLEX\n", "                fontScale = 1\n", "                color = (255, 0, 0)\n", "                thickness = 2\n", "\n", "                cv2.putText(img, f\"{r.names[int(box.cls)]}: {box.conf.item():.2f}\", org, font, fontScale, color, thickness)\n", "\n", "        annotated_frames[frame_number-1] = img.copy()\n", "\n", "        # Keep only the last 20 frames\n", "        if len(annotated_frames) > 20:\n", "            oldest_frame = min(annotated_frames.keys())\n", "            del annotated_frames[oldest_frame]\n", "    else:\n", "        img = annotated_frames[frame_number-1]\n", "\n", "    cv2.imshow('Output', img)\n", "    key = cv2.<PERSON><PERSON><PERSON>(1)\n", "\n", "    if key == ord('q'):\n", "        break\n", "    elif key == ord('p'):\n", "        paused = not paused\n", "    elif key == ord('n') and paused:\n", "        frame_number = min(frame_number + 1, total_frames - 1)\n", "    elif key == ord('b') and paused:\n", "        frame_number = max(frame_number - 1, 0)\n", "\n", "cap.release()\n", "cv2.destroyAllWindows()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Tracking"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/plain": ["-1"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["from collections import defaultdict\n", "import numpy as np\n", "video_path = \"datasets/custom videos/source videos/1.mov\"\n", "video_path = \"datasets/testrec.mp4\"\n", "cap = cv2.VideoCapture(video_path)\n", "track_history = defaultdict(lambda: [])\n", "\n", "# Loop through the video frames\n", "while cap.isOpened():\n", "    # Read a frame from the video\n", "    success, frame = cap.read()\n", "\n", "    if success:\n", "        # Run YOLO11 tracking on the frame, persisting tracks between frames\n", "        results = model.track(frame, persist=True, classes=inference_classes, verbose=False, device=device)\n", "\n", "        # Get the boxes and track IDs\n", "        boxes = results[0].boxes.xywh.cpu()\n", "        if len(boxes) > 0:\n", "            if results[0].boxes.id is not None:\n", "                track_ids = results[0].boxes.id.int().cpu().tolist()\n", "            else:\n", "                track_ids = []\n", "        else:\n", "            track_ids = []\n", "        # Visualize the results on the frame\n", "        annotated_frame = results[0].plot()\n", "\n", "        # Plot the tracks\n", "        for box, track_id in zip(boxes, track_ids):\n", "            x, y, w, h = box\n", "            track = track_history[track_id]\n", "            track.append((float(x), float(y)))  # x, y center point\n", "            if len(track) > 30:  # retain 90 tracks for 90 frames\n", "                track.pop(0)\n", "\n", "            # Draw the tracking lines\n", "            points = np.hstack(track).astype(np.int32).reshape((-1, 1, 2))\n", "            cv2.polylines(annotated_frame, [points], isClosed=False, color=(230, 230, 230), thickness=10)\n", "\n", "        # Display the annotated frame\n", "        cv2.imshow(\"YOLO11 Tracking\", annotated_frame)\n", "\n", "        # Break the loop if 'q' is pressed\n", "        if cv2.wait<PERSON>ey(1) & 0xFF == ord(\"q\"):\n", "            break\n", "    else:\n", "        # Break the loop if the end of the video is reached\n", "        break\n", "\n", "# Release the video capture object and close the display window\n", "cap.release()\n", "cv2.destroyAllWindows()\n", "cv2.<PERSON><PERSON><PERSON>(1)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 2}