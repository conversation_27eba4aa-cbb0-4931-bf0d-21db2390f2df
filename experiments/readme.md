# experiments

## detect hands
`detect_hands.py` shows the detected hand sceletons using mediapipe.

**Setup**: install miniconda and run `conda env create -f environment.yml` to create the environment. Then run `conda activate kuehl` and start the python script.

## literature
- [https://github.com/google-ai-edge/mediapipe/blob/master/docs/solutions/hands.md](https://github.com/google-ai-edge/mediapipe/blob/master/docs/solutions/hands.md)
- [https://github.com/anushkaspatil/Food-Detection](https://github.com/anushkaspatil/Food-Detection): Maybe an interessting step to begin with, but first training runs show only mediocre results.