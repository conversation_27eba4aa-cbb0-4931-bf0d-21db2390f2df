import platform
import cv2
import numpy as np


def dbg(x):
    print(x)
    return x

def is_raspberry_pi() -> bool:
    return dbg(platform.system()) == "Linux" and dbg(platform.machine()) == "aarch64"

def use_hailo() -> bool:
    return False
    # return is_raspberry_pi()


class Model:
    """
    Encapsulates model initialization and raw detection logic only.
    """
    def __init__(self, model_path="models/pt/long_aug.pt", hef_path="models/hailo/long_aug/long_aug.hef"):
        self.names = ['bacon', 'carrot', 'cucumber', 'egg', 'eggs', 'ketchup', 'mayonnaise', 'mustard', 'yogurt']
        self.model = None
        self.device = None
        self.is_hailo = False
        # Choose backend based on hardware
        if use_hailo():
            self._init_hailo(hef_path)
            self.is_hailo = True
        else:
            self._init_pytorch(model_path)

    def _init_pytorch(self, model_path):
        print("PyTorch model loading...")
        # Lazy import heavy libraries only on non-RPi
        import torch
        from ultralytics import YOLO

        self.device = (
            torch.device("cuda") if torch.cuda.is_available()
            else torch.device("mps") if torch.backends.mps.is_available()
            else torch.device("cpu")
        )
        self.model = YOLO(model_path, verbose=False)

    def _init_hailo(self, hef_path):
        print("Hailo model loading...")
        import degirum as dg

        model_name = "long_aug"
        inference_host_address = "@local"
        zoo_url = "./models/hailo"

        # load AI model
        self.model = dg.load_model(
            model_name=model_name,
            inference_host_address=inference_host_address,
            zoo_url=zoo_url
        )

    def predict(self, frame, conf_thresh=0.5, iou_thresh=0.45):
        """
        Run detection on a single frame. Returns raw confidence array and bounding boxes dict.
        """
        if self.is_hailo:
            return self._predict_hailo(frame, conf_thresh, iou_thresh)
        return self._predict_torch(frame, conf_thresh)

    def _predict_hailo(self, frame, conf_thresh: float, iou_thresh: float):
        import cv2, numpy as np

        results = self.model(frame)

        print(f"results {results}")
        results = list(results.results)
        if len(results) != 0:
            raise ValueError(f"lol")

        return self._pack_results(frame, results)


    def _predict_torch(self, frame, conf_thresh):
        # torch and YOLO already imported in init
        results = self.model.predict(
            frame, conf=conf_thresh, stream=True, device=self.device, verbose=False
        )
        boxes = next(results).boxes.cpu()
        return self._pack_results(frame, boxes)

    def _pack_results(self, frame, raw_boxes):
        """
        Convert raw model outputs into (confs, bbs) format.
        """
        h, w = frame.shape[:2]
        names = self.names
        confs = np.zeros(len(names), dtype=np.float32)
        bbs = {}

        # Handle empty detections
        if len(raw_boxes) == 0:
            print("No detections found")
            return confs, bbs

        print(f"Processing {len(raw_boxes)} detections")
        for i, box in enumerate(raw_boxes):
            print(f"Detection {i}: {box}")

            if hasattr(box, 'cls'):
                # PyTorch YOLO format
                cls = int(box.cls)
                conf = float(box.conf)
                xywhn = box.xywhn[0].numpy()
            else:
                # Numpy array format from Hailo
                box_list = box.tolist() if hasattr(box, 'tolist') else list(box)
                print(f"Box list: {box_list}")

                if len(box_list) == 0:
                    print("Empty box, skipping")
                    continue
                elif len(box_list) < 6:
                    print(f"Box has insufficient data ({len(box_list)} elements), skipping")
                    continue

                # Hailo post-processed format: [x1, y1, x2, y2, conf, cls]
                x1, y1, x2, y2, conf, cls = box_list[:6]
                cls = int(cls)

                # Convert to normalized xywh format
                xywhn = np.array([((x1+x2)/2)/w, ((y1+y2)/2)/h, (x2-x1)/w, (y2-y1)/h], dtype=np.float32)

            # Ensure class index is valid
            if 0 <= cls < len(names):
                confs[cls] = conf
                bbs[cls] = xywhn
                print(f"Added detection: class={cls} ({names[cls] if cls < len(names) else 'unknown'}), conf={conf}")
            else:
                print(f"Invalid class index {cls}, skipping")

        return confs, bbs
