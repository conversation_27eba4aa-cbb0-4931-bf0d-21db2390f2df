"""
This module handles database operations using SQLite.
It stores actions related to adding or removing items and tracks the current fridge inventory.

Database structure:
--------------------
1. actions Table                                    Logs every action of adding/removing items
   - **id** (INTEGER, PRIMARY KEY, AUTOINCREMENT)   → Unique ID of the action
   - **timestamp** (TEXT, NOT NULL)                 → Date and time of the action
   - **action_type** (TEXT, NOT NULL)               → "in" or "out" (adding or removing an item)
   - **item_class** (TEXT, NOT NULL)                → Class of the item (e.g., eggs, milk)

2. fridge_inventory Table                           Stores the current fridge content
   - **item_class** (TEXT, PRIMARY KEY)             → Class of the item (e.g., eggs, milk)
   - **quantity** (INTEGER, NOT NULL)               → Quantity of the item (can be negative)

Example usage:
--------------
db = DatabaseManager()

db.record_action("Milk", "in")
db.record_action("Eggs", "in")
db.record_action("Milk", "out")

print(db.get_fridge_inventory())
print(db.get_actions())
"""
import os
import sqlite3
from datetime import datetime

from .detect_action import Action


DB_NAME = os.path.join('ui', 'data.db')


class DatabaseManager:
    def __init__(self, klass_names, db_name=DB_NAME):
        self.db_name = db_name
        self._create_tables()
        print("Database initialized.")
        self.record_callback = lambda action, klass: self.record_action(
            klass_names[klass],
            'in' if action == Action.INSERT else 'out'
        )

    def _connect(self):
        return sqlite3.connect(self.db_name)

    def _create_tables(self):
        """Creates database tables if they don't exist."""
        with self._connect() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS actions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp TEXT,
                    action_type TEXT CHECK(action_type IN ('in', 'out')) NOT NULL,
                    item_class TEXT NOT NULL
                )
            """)
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS fridge_inventory (
                    item_class TEXT PRIMARY KEY,
                    quantity INTEGER NOT NULL
                )
            """)

    def record_action(self, item_class: str, action_type: str):
        """Records an action (adding or removing an item) and updates fridge inventory."""
        if action_type not in ("in", "out"):
            raise ValueError("Invalid action type. Use 'in' or 'out'.")

        timestamp = datetime.now().isoformat()
        with self._connect() as conn:
            cursor = conn.cursor()

            # Insert action
            cursor.execute(
                "INSERT INTO actions (timestamp, action_type, item_class) VALUES (?, ?, ?)",
                (timestamp, action_type, item_class)
            )

            # Update fridge inventory
            if action_type == "in":
                cursor.execute(
                    "INSERT INTO fridge_inventory (item_class, quantity) VALUES (?, 1) "
                    "ON CONFLICT(item_class) DO UPDATE SET quantity = quantity + 1",
                    (item_class,)
                )
            else:
                cursor.execute(
                    "INSERT INTO fridge_inventory (item_class, quantity) VALUES (?, -1) "
                    "ON CONFLICT(item_class) DO UPDATE SET quantity = quantity - 1",
                    (item_class,)
                )

    def get_fridge_inventory(self):
        """Retrieves the current fridge inventory."""
        with self._connect() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT item_class, quantity FROM fridge_inventory")
            return cursor.fetchall()

    def get_actions(self):
        """Retrieves the full history of actions."""
        with self._connect() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT timestamp, item_class, action_type FROM actions ORDER BY timestamp DESC")
            return cursor.fetchall()
