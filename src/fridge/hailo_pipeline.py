import cv2
import numpy as np
import threading
import queue
import time
from pathlib import Path

try:
    import gi
    gi.require_version('Gst', '1.0')
    from gi.repository import Gst, GLib
    HAILO_AVAILABLE = True
except ImportError:
    HAILO_AVAILABLE = False
    print("Warning: Hailo dependencies not available. HailoPipeline will not work.")


class HailoPipeline:
    """
    A simplified Hailo inference pipeline that accepts HEF model path and provides
    a PyTorch-like interface with constructor and predict function.

    Based on hailo-apps-infra GStreamer implementation but simplified for direct frame input.
    """

    def __init__(self, hef_path: str, input_width: int = 640, input_height: int = 640):
        """
        Initialize the Hailo pipeline with a HEF model file.

        Args:
            hef_path: Path to the HEF model file
            input_width: Expected input width for the model
            input_height: Expected input height for the model
        """
        if not HAILO_AVAILABLE:
            raise RuntimeError("Hailo dependencies not available. Install hailort and ensure GStreamer is available.")

        self.hef_path = Path(hef_path)
        if not self.hef_path.exists():
            raise FileNotFoundError(f"HEF file not found: {hef_path}")

        self.input_width = input_width
        self.input_height = input_height
        self.pipeline = None
        self.loop = None
        self.appsrc = None
        self.appsink = None
        self.result_queue = queue.Queue(maxsize=5)
        self.running = False
        self.pipeline_thread = None

        # Initialize GStreamer
        Gst.init(None)

        # Create the pipeline
        self._create_pipeline()

    def _create_pipeline(self):
        """Create the GStreamer pipeline for Hailo inference."""
        # Create pipeline string - simplified version of the GStreamer app
        pipeline_str = (
            f"appsrc name=source is-live=true format=time "
            f"caps=video/x-raw,format=RGB,width={self.input_width},height={self.input_height},framerate=30/1 ! "
            f"videoconvert ! "
            f"hailonet hef-path={self.hef_path} ! "
            f"hailofilter so-path=/usr/lib/aarch64-linux-gnu/gstreamer-1.0/libgsthailofilter.so "
            f"config-path={self._get_config_path()} ! "
            f"videoconvert ! "
            f"appsink name=sink emit-signals=true sync=false"
        )

        try:
            self.pipeline = Gst.parse_launch(pipeline_str)
        except Exception as e:
            print(f"Error creating pipeline: {e}")
            # Fallback to simpler pipeline without hailofilter
            pipeline_str = (
                f"appsrc name=source is-live=true format=time "
                f"caps=video/x-raw,format=RGB,width={self.input_width},height={self.input_height},framerate=30/1 ! "
                f"videoconvert ! "
                f"hailonet hef-path={self.hef_path} ! "
                f"videoconvert ! "
                f"appsink name=sink emit-signals=true sync=false"
            )
            self.pipeline = Gst.parse_launch(pipeline_str)

        # Get pipeline elements
        self.appsrc = self.pipeline.get_by_name("source")
        self.appsink = self.pipeline.get_by_name("sink")

        if not self.appsrc or not self.appsink:
            raise RuntimeError("Failed to get pipeline elements")

        # Connect appsink callback
        self.appsink.connect("new-sample", self._on_new_sample)

        # Set up bus for error handling
        bus = self.pipeline.get_bus()
        bus.add_signal_watch()
        bus.connect("message", self._bus_call)

    def _get_config_path(self):
        """Get the config path for the HEF model if it exists."""
        config_path = self.hef_path.parent / f"{self.hef_path.stem}.json"
        if config_path.exists():
            return str(config_path)
        return ""

    def _bus_call(self, bus, message):
        """Handle GStreamer bus messages."""
        t = message.type
        if t == Gst.MessageType.ERROR:
            err, debug = message.parse_error()
            print(f"Pipeline error: {err}, {debug}")
            self.running = False
        elif t == Gst.MessageType.EOS:
            print("End of stream")
            self.running = False
        return True

    def _on_new_sample(self, appsink):
        """Callback for new samples from appsink."""
        sample = appsink.emit('pull-sample')
        if sample:
            buffer = sample.get_buffer()
            if buffer:
                # Extract frame data and metadata
                caps = sample.get_caps()
                structure = caps.get_structure(0)
                width = structure.get_int('width')[1]
                height = structure.get_int('height')[1]

                # Get buffer data
                success, map_info = buffer.map(Gst.MapFlags.READ)
                if success:
                    # Convert to numpy array
                    frame_data = np.frombuffer(map_info.data, dtype=np.uint8)
                    frame = frame_data.reshape((height, width, 3))

                    # Extract detection results from buffer metadata
                    detections = self._extract_detections(buffer)

                    # Put result in queue
                    try:
                        self.result_queue.put((frame, detections), block=False)
                    except queue.Full:
                        # Drop frame if queue is full
                        pass

                    buffer.unmap(map_info)

        return Gst.FlowReturn.OK

    def _extract_detections(self, buffer):
        """Extract detection results from buffer metadata."""
        # This is a simplified version - in practice, you'd extract
        # detection metadata from the buffer or use Hailo's post-processing
        # For now, return empty detections
        return []

    def _start_pipeline(self):
        """Start the GStreamer pipeline."""
        if self.running:
            return

        self.running = True

        # Create GLib main loop
        self.loop = GLib.MainLoop()

        # Set pipeline to playing state
        ret = self.pipeline.set_state(Gst.State.PLAYING)
        if ret == Gst.StateChangeReturn.FAILURE:
            raise RuntimeError("Failed to start pipeline")

        # Start the main loop in a separate thread
        self.pipeline_thread = threading.Thread(target=self.loop.run)
        self.pipeline_thread.daemon = True
        self.pipeline_thread.start()

        # Wait a bit for pipeline to start
        time.sleep(0.1)

    def _stop_pipeline(self):
        """Stop the GStreamer pipeline."""
        if not self.running:
            return

        self.running = False

        # Stop pipeline
        if self.pipeline:
            self.pipeline.set_state(Gst.State.NULL)

        # Stop main loop
        if self.loop:
            self.loop.quit()

        # Wait for thread to finish
        if self.pipeline_thread and self.pipeline_thread.is_alive():
            self.pipeline_thread.join(timeout=1.0)

    def predict(self, frame: np.ndarray, conf_thresh: float = 0.5, iou_thresh: float = 0.45):
        """
        Run inference on a single frame.

        Args:
            frame: Input frame as numpy array (H, W, 3) in RGB format
            conf_thresh: Confidence threshold for detections
            iou_thresh: IoU threshold for NMS (not used in this implementation)

        Returns:
            Tuple of (confidences, bounding_boxes) similar to the Model class interface
        """
        if not HAILO_AVAILABLE:
            raise RuntimeError("Hailo dependencies not available")

        # Start pipeline if not running
        if not self.running:
            self._start_pipeline()

        # Resize frame to expected input size
        if frame.shape[:2] != (self.input_height, self.input_width):
            frame = cv2.resize(frame, (self.input_width, self.input_height))

        # Ensure frame is in RGB format and uint8
        if frame.dtype != np.uint8:
            frame = frame.astype(np.uint8)

        # Create GStreamer buffer from frame
        buffer = Gst.Buffer.new_wrapped(frame.tobytes())

        # Set buffer timestamp
        buffer.pts = int(time.time() * Gst.SECOND)
        buffer.duration = Gst.util_uint64_scale_int(1, Gst.SECOND, 30)  # 30 FPS

        # Push buffer to appsrc
        ret = self.appsrc.emit('push-buffer', buffer)
        if ret != Gst.FlowReturn.OK:
            print(f"Failed to push buffer: {ret}")
            return np.zeros(9, dtype=np.float32), {}

        # Wait for result with timeout
        try:
            result_frame, detections = self.result_queue.get(timeout=1.0)
            return self._process_detections(detections, frame.shape)
        except queue.Empty:
            print("Timeout waiting for inference result")
            return np.zeros(9, dtype=np.float32), {}

    def _process_detections(self, detections, frame_shape):
        """
        Process raw detections into the format expected by the Model class.

        Args:
            detections: Raw detection results from Hailo
            frame_shape: Shape of the input frame

        Returns:
            Tuple of (confidences, bounding_boxes) matching Model class interface
        """
        # Class names matching the model
        names = ['bacon', 'carrot', 'cucumber', 'egg', 'eggs', 'ketchup', 'mayonnaise', 'mustard', 'yogurt']

        # Initialize outputs
        confs = np.zeros(len(names), dtype=np.float32)
        bbs = {}

        # Process detections (simplified - would need actual Hailo post-processing)
        for detection in detections:
            # This would need to be implemented based on actual Hailo output format
            # For now, return empty results
            pass

        return confs, bbs

    def __del__(self):
        """Cleanup when object is destroyed."""
        self._stop_pipeline()

    def __enter__(self):
        """Context manager entry."""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        self._stop_pipeline()


# Example usage (commented out to avoid execution during import)
"""
if __name__ == "__main__":
    import cv2

    # Initialize the pipeline
    hef_path = "models/hailo/long_aug/long_aug.hef"
    pipeline = HailoPipeline(hef_path)

    # Load and process a test image
    frame = cv2.imread("test_image.jpg")
    frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)

    # Run inference
    confidences, bounding_boxes = pipeline.predict(frame_rgb)

    print(f"Confidences: {confidences}")
    print(f"Bounding boxes: {bounding_boxes}")

    # Clean up
    pipeline._stop_pipeline()
"""