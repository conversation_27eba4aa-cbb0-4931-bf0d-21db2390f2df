import types
import numpy as np

from . import detect_action
from .db_manager import DatabaseManager
from .detect_action import State
from .model import Model

class Session:
    frame_counter: int
    frame_generator: None | types.GeneratorType
    frame_source: str
    conf_history: list[np.ndarray]
    action_state: State
    db: DatabaseManager
    model: Model


    def __init__(self):
        self.model = Model()
        self.db = DatabaseManager(self.model.names)
        self.clear_source()


    def set_source(self, generator, source_name):
        self.frame_generator = generator
        self.frame_source = source_name
        self.frame_counter = 0
        self.action_state = State()
        self.conf_history = []


    def has_source(self):
        return self.frame_generator is not None


    def clear_source(self):
        self.set_source(None, "None")


    def frames(self):
        while True:
            if self.frame_generator is None:
                yield None
            else:
                for frame in self.frame_generator:
                    yield frame
                    if self.frame_generator is None:
                        break
                    self.frame_counter += 1
                self.clear_source()


    def process(self, frame):
        if frame is None:
            return None, None
        confs, bbs = self.model.predict(frame)
        detect_action.on_frame(self.action_state, bbs, confs, self.db.record_callback)
        self.conf_history.append(confs)
        if len(self.conf_history) > 60:
            self.conf_history.pop(0)
        return confs, bbs
        
