from dataclasses import dataclass, field
from enum import Enum
import numpy as np
import math

# Smoothing and detection parameters.
WINDOW_SIZE = 5
DETECTION_THRESHOLD = 0.6
GRACE_FRAMES = 1
NO_CLASS = -1


def _normalize(vec: np.ndarray) -> np.ndarray:
    mag = _vec_magnitude(vec)
    return vec / mag if mag != 0 else vec


def _vec_magnitude(vec: np.ndarray) -> float:
    return math.sqrt(np.dot(vec, vec))


def _is_close_to_edge(x: float) -> bool:
    centre = 0.5
    closeness = 0.15
    return (x - centre) ** 2 > (centre - closeness) ** 2


class Action(Enum):
    INSERT = 1
    REMOVE = 2


class BoundingBoxes:
    def __init__(self, N):
        self.N = N
        self.first = np.zeros((N, 2))
        self.last = np.zeros((N, 2))


    def reset(self):
        self.first = np.zeros((self.N, 2))
        self.last = np.zeros((self.N, 2))


    def update(self, bb: dict):
        for key, val in bb.items():
            if np.all(self.first[key] == 0):
                self.first[key] = val[:2]
            self.last[key] = val[:2]


@dataclass
class State:
    prev_class: int = NO_CLASS
    # A history of probability vectors (one per frame).
    prob_history: list = field(default_factory=list)
    grace_counter: int = GRACE_FRAMES
    bbs: BoundingBoxes = BoundingBoxes(9)


    def ask_for_grace(self):
        """
        Returns True if we should commit a class change.
        """
        if self.prev_class == NO_CLASS or self.grace_counter == 0:
            return True
        self.grace_counter -= 1
        return False


    def commit(self, emit):
        if self.prev_class == NO_CLASS:
            return
        # Reset grace counter
        self.grace_counter = GRACE_FRAMES

        first_pos = self.bbs.first[self.prev_class]
        last_pos = self.bbs.last[self.prev_class]
        # Calculate the direction of motion from first to last positions.
        dir = _normalize(last_pos - first_pos)
        up = np.array([0, 1])
        cosine = np.dot(dir, up)
        if cosine > 0.6 and last_pos[1] > 0.5:
            # Object moved upwards.
            emit(Action.INSERT, self.prev_class)
        elif cosine < -0.6 and (last_pos[1] < 0.25 or _is_close_to_edge(last_pos[0])):
            # Object moved downwards.
            emit(Action.REMOVE, self.prev_class)
        else:
            # Movement is ambiguous.
            pass


def print_emit(action: Action, klass: int):
    print(f'emit {action} {classes[klass]}')


def on_frame(state: State, bb: dict[int, np.ndarray], probs: np.ndarray,
             emit = print_emit):
    """
    Processes a new frame. Instead of taking a single class label, it accepts a probability
    vector for all classes. It accumulates the probabilities over a sliding window (WINDOW_SIZE)
    to compute an average. If the best class (the one with highest average probability)
    meets a detection threshold, it is used; otherwise, the detection is ignored (-1).
    The bounding box (bb) is used to update the object's position.
    """
    # Update the probability history.
    state.prob_history.append(probs)
    if len(state.prob_history) > WINDOW_SIZE:
        state.prob_history.pop(0)
    # Compute the average probability vector over the history.
    avg_probs = np.mean(state.prob_history, axis=0)
    best_class = int(np.argmax(avg_probs))
    best_prob = avg_probs[best_class]
    current_class = best_class if best_prob >= DETECTION_THRESHOLD else -1

    if state.prev_class != current_class:
        if state.ask_for_grace():
            state.commit(emit)
            state.prev_class = current_class
            # Reset tracking when class changes.
            state.bbs.reset()
            # Reset probability history to start fresh.
            state.prob_history = [probs]
    state.bbs.update(bb)


# Example classes.
classes = {
    0: 'bacon',
    1: 'carrot',
    2: 'cucumber',
    3: 'egg',
    4: 'eggs',
    5: 'ketchup',
    6: 'mayonnaise',
    7: 'mustard',
    8: 'yogurt'
}
