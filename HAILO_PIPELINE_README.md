# HailoPipeline Implementation

This document describes the new `HailoPipeline` class that provides a PyTorch-like interface for Hailo AI inference using GStreamer and HailoRT.

## Overview

The `HailoPipeline` class is based on the [hailo-apps-infra GStreamer implementation](https://github.com/hailo-ai/hailo-apps-infra/blob/main/hailo_apps/hailo_app_python/core/gstreamer/gstreamer_app.py) but simplified to provide a clean interface similar to PyTorch models.

## Key Features

- **Simple Interface**: Just constructor and `predict()` method, similar to PyTorch models
- **Manual Frame Input**: Feed frames directly without command-line setup
- **GStreamer-based**: Uses native HailoRT and GStreamer for optimal performance
- **Context Manager Support**: Proper resource cleanup with `with` statements
- **Thread-safe**: Pipeline runs in background thread

## Usage

### Basic Usage

```python
from src.fridge.hailo_pipeline import HailoPipeline
import cv2
import numpy as np

# Initialize pipeline with HEF model
pipeline = HailoPipeline("models/hailo/long_aug/long_aug.hef")

# Load and preprocess image
frame = cv2.imread("image.jpg")
frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)

# Run inference
confidences, bounding_boxes = pipeline.predict(frame_rgb)

# Clean up
pipeline._stop_pipeline()
```

### Context Manager Usage

```python
with HailoPipeline("models/hailo/long_aug/long_aug.hef") as pipeline:
    confidences, bbs = pipeline.predict(frame)
    # Automatic cleanup when exiting context
```

### Integration with Model Class

The `HailoPipeline` can be used as an alternative to the existing `degirum`-based Hailo implementation:

```python
# In model.py, set use_hailo_pipeline() to return True
def use_hailo_pipeline() -> bool:
    return True  # Enable HailoPipeline

# Then use Model class normally
from src.fridge.model import Model
model = Model()
confidences, bbs = model.predict(frame)
```

## Architecture

### GStreamer Pipeline

The pipeline uses the following GStreamer elements:

```
appsrc -> videoconvert -> hailonet -> hailofilter -> videoconvert -> appsink
```

- **appsrc**: Receives frames from Python
- **hailonet**: Runs inference on Hailo hardware
- **hailofilter**: Post-processes detection results
- **appsink**: Outputs results back to Python

### Threading Model

- Main thread: Handles `predict()` calls and frame input
- Pipeline thread: Runs GStreamer main loop
- Queue-based communication between threads

## Dependencies

### Required Packages

- `hailort>=4.21.0` (ARM64 Linux only)
- `gi` (GObject Introspection)
- `gstreamer1.0-plugins-*` (GStreamer plugins)
- `opencv-python`
- `numpy`

### Installation on Raspberry Pi

```bash
# Install GStreamer and Hailo dependencies
sudo apt update
sudo apt install python3-gi gstreamer1.0-plugins-base gstreamer1.0-plugins-good

# Install Python packages (already configured in pyproject.toml)
uv sync
```

## Configuration

### Model Files

The pipeline expects:
- `*.hef` file: Compiled Hailo model
- `*.json` file: Optional configuration (same name as HEF)

Example structure:
```
models/hailo/long_aug/
├── long_aug.hef
└── long_aug.json
```

### Input Requirements

- **Format**: RGB (not BGR)
- **Size**: Must match model input size (default: 640x640)
- **Type**: `numpy.ndarray` with `dtype=uint8`

## Comparison with Existing Implementation

| Feature | Degirum (Current) | HailoPipeline (New) |
|---------|-------------------|---------------------|
| Interface | High-level API | GStreamer-based |
| Performance | Good | Potentially better |
| Control | Limited | Full pipeline control |
| Dependencies | `degirum` package | `hailort` + GStreamer |
| Complexity | Simple | Moderate |

## Troubleshooting

### Common Issues

1. **Import Error**: Ensure GStreamer and HailoRT are installed
2. **Pipeline Creation Failed**: Check HEF file path and permissions
3. **No Results**: Verify input frame format (RGB, correct size)
4. **Timeout**: Increase timeout in `predict()` method

### Debug Mode

Enable GStreamer debug output:
```bash
export GST_DEBUG=3
python test_hailo_pipeline.py
```

## Testing

Run the test script to verify installation:

```bash
python test_hailo_pipeline.py
```

This will test both the standalone `HailoPipeline` and integration with the `Model` class.

## Future Improvements

- [ ] Implement proper detection extraction from buffer metadata
- [ ] Add support for different input formats
- [ ] Optimize pipeline for better performance
- [ ] Add batch processing support
- [ ] Implement proper error handling and recovery
