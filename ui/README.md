# Fridge Inventory App

This full-stack app displays DB data and consists of two parts:  

### **Backend (Node.js, TypeScript, SQLite Database)**
- Communicates with DB
- Hosts static files (only in production mode)
- Provides REST API
- Provides WebSocket for new data notification

### **Frontend (Vue.js, TypeScript)**
- Communicates with the backend
- Renders data

---

## 👇️ **Check Requirements**

- **Node.js** (>= 20.x)
- **npm** (comes with Node.js)

1. **Download & Install:**
    - Go to [nodejs.org](https://nodejs.org) and download the **LTS version** for your OS.
    - Run the installer and follow the setup instructions.

2. **Ensure Node.js and npm are installed:**
```bash
node --version
npm --version
```

---

## 📦️ **Install Dependencies**

Install npm dependencies (backend + frontend):
```bash
npm install
```

---

## 🏗️ **Run in Production Mode**
In production, the Vue app (frontend) is built into the frontend/dist folder and served as static files through the Node.js (backend) server.

Build and run:
```bash
npm start
```

The server will display the local network address in the console.

---

## 🚀 **Run in Development Mode**
In development, Vue app (frontend) and Node (backend) servers run separately for hot reloads.

Start both servers:
```bash
npm run dev
```

Vue app server (frontend) runs at:

    http://localhost:8080

Node server (backend) runs at:

    http://localhost:3000

Both servers reload automatically when changes are made.