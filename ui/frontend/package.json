{"name": "fridge-inventory-app-frontend", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build"}, "dependencies": {"axios": "^1.8.4", "chart.js": "^4.4.8", "core-js": "^3.8.3", "pinia": "^3.0.1", "qrcode.vue": "^3.6.0", "socket.io-client": "^4.8.1", "vue": "^3.2.13"}, "devDependencies": {"@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-typescript": "~5.0.0", "@vue/cli-service": "~5.0.0", "typescript": "~4.5.5"}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"]}