import {defineStore} from 'pinia';
import {getActions, getActionsReport} from '@/backendCommunication/api';
import {Action} from '@/types/action';
import {ActionsReportItem} from '@/types/actionsReportItem';

export const useActionsStore = defineStore('actions', {
  state: () => ({
    actions: [] as Action[],
    actionsReport: [] as ActionsReportItem[],
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0
  }),
  actions: {
    // Fetch actions with pagination
    async fetchActions(page = 1, limit = 100) {
      const response = await getActions(page, limit);
      this.actions = response.data;
      this.page = response.page;
      this.limit = response.limit;
      this.total = response.total;
      this.totalPages = response.totalPages;
    },

    // Fetch actions report (30-day summary)
    async fetchActionsReport() {
      this.actionsReport = await getActionsReport();
    }
  }
});