<script setup lang="ts">
import { defineProps, defineEmits, computed } from 'vue';
import QrcodeVue from 'qrcode.vue';

const props = defineProps<{ modelValue: boolean; link: string }>();
const emit = defineEmits(['update:modelValue']);

const localShown = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
});

const toggleVisibility = () => {
  localShown.value = !localShown.value;
};
</script>

<template>
  <transition name="fade">
    <div class="qr-overlay" v-if="localShown">
      <div class="qr-code-container">
        <QrcodeVue :size="200" :value="link" level="H" />
      </div>
      <button class="close-button" @click="toggleVisibility">Close</button>
    </div>
  </transition>
</template>

<style scoped>
/* Transition Styles */
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.3s ease-in-out;
}
.fade-enter-from, .fade-leave-to {
  opacity: 0;
}

/* QR Overlay */
.qr-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: #FFFFFFdd;
}

/* Close Button */
.close-button {
  padding: 8px 0;
  width: 200px;
  margin-top: var(--margin-big);
  background-color: var(--header-bg-color);
  color: white;
  border: none;
  border-radius: 1000px;
  font-size: var(--headline-font-size);
  cursor: pointer;
}
</style>