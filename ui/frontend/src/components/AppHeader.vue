<script setup>
import {ref, onMounted} from 'vue';
import QrCode from '@/components/QrCode.vue';

const currentUrl = ref('');

onMounted(() => {
  currentUrl.value = window.location.href; // Gets the current page URL
});

const copyUrlToClipboard = () => {
  navigator.clipboard
      .writeText(currentUrl.value)
      .catch(err => {
        console.error('Failed to copy url:', err);
      });
};

const isQrShown = ref(false);
</script>

<template>
  <header>
    <h1 class="title">Your Fridge Overview</h1>
    <div class="link-row">
      <a :href="currentUrl" class="link" target="_blank">{{ currentUrl }}</a>
      <button class="link-button" @click="copyUrlToClipboard">
        <svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#5f6368">
          <path
              d="M368.37-237.37q-37.78 0-64.39-26.61t-26.61-64.39v-474.26q0-37.78 26.61-64.39t64.39-26.61h354.26q37.78 0 64.39 26.61t26.61 64.39v474.26q0 37.78-26.61 64.39t-64.39 26.61H368.37Zm-171 171q-37.78 0-64.39-26.61t-26.61-64.39v-565.26h91v565.26h445.26v91H197.37Z"/>
        </svg>
      </button>
      <button class="link-button" @click="isQrShown = true">
        <svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#5f6368">
          <path
              d="M109-520v-331h331v331H109Zm91-91h149v-149H200v149Zm-91 502v-331h331v331H109Zm91-91h149v-149H200v149Zm320-320v-331h331v331H520Zm91-91h149v-149H611v149Zm157.37 502v-82.87H851V-109h-82.63ZM520-357.37V-440h82.87v82.63H520Zm82.87 82.87v-82.87h82.63v82.87h-82.63ZM520-191.87v-82.63h82.87v82.63H520ZM602.87-109v-82.87h82.63V-109h-82.63Zm82.63-82.87v-82.63h82.87v82.63H685.5Zm0-165.5V-440h82.87v82.63H685.5Zm82.87 82.87v-82.87H851v82.87h-82.63Z"/>
        </svg>
      </button>
    </div>
    <p class="description">Available in your local network</p>
    <QrCode :link="currentUrl" v-model="isQrShown"/>
  </header>
</template>

<style scoped>
.title {
  font-weight: 400;
  font-size: var(--title-font-size);
  margin-bottom: 10px;
  color: white;
}

.link-row {
  display: flex;
  align-items: center;
}

.link {
  color: #EEEEEE;
  font-size: var(--large-body-font-size);
  text-decoration: none;
  margin-right: 15px;
}

.link-button {
  width: 30px;
  height: 30px;
  border: none;
  background: transparent;
  cursor: pointer;
}

.link-button svg {
  width: 80%;
  height: 80%;
}

.link-button path {
  fill: #CCCCCC;
  transition: fill var(--transition-duration) ease-in-out;
}

.link-button:hover path {
  fill: #EEEEEE;
}

.description {
  color: #CCCCCC;
  font-size: var(--small-body-font-size);
}

@media (max-width: 768px) {
  .header {
    background-color: var(--header-bg-color);
    padding: var(--margin-big) 0;

    display: flex;
    flex-direction: column;
    align-items: center;
  }
}
</style>