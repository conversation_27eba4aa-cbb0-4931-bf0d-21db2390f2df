<script setup>
defineProps({
  title: {
    type: String,
    required: true
  }
});
</script>

<template>
  <div class="card">
    <h2 class="card-title">{{ title }}</h2>
    <slot></slot>
  </div>
</template>

<style scoped>
.card {
  position: relative;
  min-width: 400px;
  min-height: 200px;
  border-radius: var(--large-border-radius);
  background-color: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.card-title {
  display: flex;
  width: 100%;
  height: 80px;
  padding: 0 var(--margin-big);
  align-items: center;
  justify-content: center;
  font-size: var(--headline-font-size);
  color: var(--text-color);
  border-bottom: 2px solid #e3e3e3;
}

@media (max-width: 1279px) {
  .card {
    min-width: 300px;
  }
}

@media (max-width: 950px) {
  .card {
    min-width: 250px;
  }
}

@media (max-width: 768px) {
  .card {
    border-radius: 0;
    box-shadow: none;
  }
}
</style>