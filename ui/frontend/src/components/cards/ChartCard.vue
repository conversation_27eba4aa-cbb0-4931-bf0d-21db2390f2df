<script setup lang="ts">
import {defineProps, onMounted, watch, useTemplateRef} from 'vue';
import {ActionsReportItem} from '@/types/actionsReportItem';
import {ChartHelper} from '@/components/cards/chartHelper';
import Card from '@/components/cards/Card.vue';

const props = defineProps<{ actionsReport: ActionsReportItem[] }>();

const chartRef = useTemplateRef<HTMLCanvasElement>('chart');
let chartHelper: ChartHelper;

onMounted(() => {
  if (!chartRef.value) return;

  chartHelper = new ChartHelper(chartRef.value);
  chartHelper.createChart(props.actionsReport);
});

// Watch for updates and refresh the chart smoothly
watch(() => props.actionsReport, (newActionsReport) => {
  chartHelper?.updateChart(newActionsReport);
});
</script>

<template>
  <Card title="🔥 Frequent Picks in 30 Days">
    <canvas ref="chart" class="canvas"></canvas>
  </Card>
</template>

<style scoped>
.canvas {
  margin: 0 30px 0 20px;
}
</style>