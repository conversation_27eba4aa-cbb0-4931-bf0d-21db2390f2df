<script setup lang="ts">
import Card from '@/components/cards/Card.vue';
import {computed, defineProps} from 'vue';
import {Action} from '@/types/action';

const props = defineProps<{ actions: Action[] }>();

const formatDate = (dateStr: string) => {
  const today = new Date();
  const yesterday = new Date();
  yesterday.setDate(today.getDate() - 1);

  const date = new Date(dateStr);
  if (date.toDateString() === today.toDateString()) return 'today';
  if (date.toDateString() === yesterday.toDateString()) return 'yesterday';
  return date.toLocaleDateString('de-DE');
};

const formatTime = (timestamp: string) => {
  return new Date(timestamp).toLocaleTimeString('en-GB', {hour: '2-digit', minute: '2-digit'});
};

const formatAction = (itemName: string, actionType: 'in' | 'out') => {
  itemName = `<b>and this is bold text</b>`;
  return actionType === 'in' ? `Put in ${itemName}` : `Take out ${itemName}`;
};

const formatActionType = (actionType: 'in' | 'out') => {
  return actionType === 'in' ? 'Put in' : 'Take out';
}

const formatItemClass = (itemClass: String) => {
  return itemClass.toLowerCase()
}

const groupedActions = computed(() => {
  return props.actions.reduce((acc, entry) => {
    const date = entry.timestamp.split('T')[0];
    if (!acc[date]) acc[date] = [];
    acc[date].push(entry);
    return acc;
  }, {} as Record<string, Action[]>);
});
</script>

<template>
  <Card title="🕒 Recent Activity">
    <div class="table-container">
      <table>
        <thead>
        <tr>
          <th class="date-col">📅 date</th>
          <th class="time-col">⏰ time</th>
          <th class="action-col">📝 action</th>
        </tr>
        </thead>
        <tbody>
        <template v-for="(group, date) in groupedActions" :key="date">
          <tr v-for="(entry, index) in group" :key="date + index">
            <td v-if="index === 0" :rowspan="group.length" class="date-cell">{{ formatDate(date) }}</td>
            <td>{{ formatTime(entry.timestamp) }}</td>
            <td>
              {{formatActionType(entry.action_type)}}
              <b>{{formatItemClass(entry.item_class)}}</b>
            </td>
          </tr>
        </template>
        </tbody>
      </table>
    </div>
  </Card>
</template>

<style scoped>
.table-container {
  overflow-x: auto;
  max-width: 100%;
}

table {
  padding-left: 20px;
  width: 100%;
  border-collapse: collapse;
}

th, td {
  padding: 8px 20px;
  border: 1px solid #E3E3E3;
  text-align: left;
  font-weight: normal;
  color: #646464;
}

th:first-child, .date-cell {
  padding-left: var(--margin-big);
}

th {
  background-color: #f8f8f8;
  font-size: var(--large-body-font-size);
}

td {
  font-size: var(--small-body-font-size);
}

.date-col, .time-col {
  width: 27%;
}

.date-cell {
  vertical-align: top;
}

.action-col {
  width: auto;
}
</style>