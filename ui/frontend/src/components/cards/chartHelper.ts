import {Chart, CategoryScale, BarElement, Title, Tooltip, Legend, LinearScale, BarController} from 'chart.js';
import {ActionsReportItem} from '@/types/actionsReportItem';

Chart.register(CategoryScale, BarElement, Title, Tooltip, Legend, LinearScale, BarController);

export class ChartHelper {
  private chartInstance: Chart | null = null;
  private colorScale = [
    '#d4eaf5', // Lightest Blue
    '#a8d4eb', // Lighter Blue
    '#7cbfe1', // Soft Medium Blue
    '#50aad7', // Medium Blue
    '#2485cd', // Slightly Darker Blue
    '#006ab4'  // Darkest Blue
  ];

  constructor(private chartRef: HTMLCanvasElement) {
  }

  private getColorForValue(value: number, minValue: number, maxValue: number): string {
    // Avoid division by zero
    if (minValue === maxValue) {
      const index = Math.floor(this.colorScale.length / 2);
      return this.colorScale[index];
    }

    const normalizedValue = (value - minValue) / (maxValue - minValue);
    const index = Math.min(
      Math.floor(normalizedValue * (this.colorScale.length - 1)),
      this.colorScale.length - 1
    );

    return this.colorScale[index];
  }

  private processData(actionsReport: ActionsReportItem[]) {
    const sortedReport = [...actionsReport].sort((a, b) => b.actions - a.actions);
    const minValue = Math.min(...sortedReport.map(item => item.actions));
    const maxValue = Math.max(...sortedReport.map(item => item.actions));

    const labels = sortedReport.map(item => item.item_class);
    const data = sortedReport.map(item => item.actions);
    const colors = sortedReport.map(item => this.getColorForValue(item.actions, minValue, maxValue));

    return {labels, data, colors};
  }

  createChart(actionsReport: ActionsReportItem[]) {
    const {labels, data, colors} = this.processData(actionsReport);

    this.chartInstance = new Chart(this.chartRef, {
      type: 'bar',
      data: {
        labels,
        datasets: [{
          label: 'Actions Count',
          data,
          backgroundColor: colors
        }]
      },
      options: {
        indexAxis: 'y', // Horizontal bars
        responsive: true,
        scales: {
          x: {
            beginAtZero: true,
            grid: {display: false},
            ticks: {display: false}
          },
          y: {
            grid: {display: false},
            ticks: {font: {size: 12}}
          }
        },
        plugins: {
          legend: {display: false}
        },
        animation: {
          duration: 800
        },
        elements: {
          bar: {
            borderRadius: 100
          }
        }
      }
    });
  }

  updateChart(actionsReport: ActionsReportItem[]) {
    if (!this.chartInstance) return;

    const {labels, data, colors} = this.processData(actionsReport);

    this.chartInstance.data.labels = labels;
    this.chartInstance.data.datasets[0].data = data;
    this.chartInstance.data.datasets[0].backgroundColor = colors;

    this.chartInstance.update('none'); // Smooth update
  }
}