<script setup lang="ts">
import {defineProps, ref, watch} from 'vue';
import {Item} from '@/types/item';
import Card from '@/components/cards/Card.vue';

const props = defineProps<{ items: Item[] }>();

// Track item changes for animations
const animatedItems = ref(props.items.filter(item => item.quantity !== 0));

watch(
    () => props.items,
    (newItems) => {
      // Update animatedItems with filtered items
      animatedItems.value = newItems.filter(item => item.quantity !== 0);
    },
    {deep: true}
);
</script>

<template>
  <Card title="🧊 Fridge Inventory">
    <transition-group name="list" tag="ul" class="item-list">
      <li v-for="item in animatedItems" :key="item.item_class" class="item">
        {{ item.item_class }} ({{ item.quantity }})
      </li>
    </transition-group>
  </Card>
</template>

<style scoped>
.item-list {
  padding: var(--margin-big);
  list-style: none;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.item {
  position: relative;
  padding-left: 20px;
  font-size: var(--large-body-font-size);
  color: var(--text-color);
  transition: transform 3s ease-in-out;
}

.item::before {
  content: "•"; /* Unicode bullet */
  position: absolute;
  left: 0;
  top: 0;
  color: var(--text-color);
  font-size: var(--large-body-font-size);
}

/* Animation for new/removing items */
.list-move,
.list-enter-active,
.list-leave-active {
  transition: all var(--long-transition-duration) ease !important;
}

.list-enter-from,
.list-leave-to {
  opacity: 0;
  transform: translateX(30px);
}
</style>