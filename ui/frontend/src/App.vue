<script setup lang="ts">
import AppHeader from '@/components/AppHeader.vue';
import Panel from '@/components/Panel.vue';
import ContentCard from '@/components/cards/ContentCard.vue';
import ChartCard from '@/components/cards/ChartCard.vue';
import HistoryCard from '@/components/cards/HistoryCard.vue';
import {onMounted} from 'vue';
import {useFridgeStore} from '@/stores/fridgeStore';
import {useActionsStore} from '@/stores/actionsStore';
import {setupNewDataListener} from '@/backendCommunication/webSocket';

const fridgeStore = useFridgeStore();
const actionsStore = useActionsStore();

onMounted(() => {
  fridgeStore.fetchFridgeContent();
  actionsStore.fetchActions();
  actionsStore.fetchActionsReport();

  setupNewDataListener(() => {
    fridgeStore.fetchFridgeContent();
    actionsStore.fetchActions();
    actionsStore.fetchActionsReport();
  });
});
</script>

<template>
  <Panel class="panel"/>

  <div class="grid-container">
    <AppHeader class="header"/>

    <template v-if="fridgeStore.items.length || actionsStore.actions.length || actionsStore.actionsReport.length">
      <ContentCard v-if="fridgeStore.items.length" class="content-card" :items="fridgeStore.items"/>
      <ChartCard v-if="actionsStore.actionsReport.length" class="chart-card"
                 :actions-report="actionsStore.actionsReport"/>
      <HistoryCard v-if="actionsStore.actions.length" class="history-card" :actions="actionsStore.actions"/>
    </template>

    <div v-else class="placeholder">
      Place an item in your fridge to start tracking statistics.
    </div>
  </div>
</template>

<style>
.grid-container {
  display: grid;
  margin: calc(2.5 * var(--margin-big)) calc(2.5 * var(--margin-big)) var(--margin-big);
  grid-template-columns: 1fr 3fr; /* First column fixed, second flexible */
  grid-template-rows: auto auto auto auto; /* Header + 2 content rows */
  grid-template-areas:
    "header header"
    "content chart"
    "content history"
    "placeholder placeholder";
  gap: calc(0.8 * var(--margin-big));
  align-items: start; /* Prevents vertical stretching */
}

.header {
  grid-area: header;
  margin-bottom: calc(0.5 * var(--margin-big));
}

.content-card {
  grid-area: content;
}

.chart-card {
  grid-area: chart;
}

.history-card {
  grid-area: history;
}

.placeholder {
  margin-top: 200px;
  grid-area: placeholder;
  text-align: center;
  font-size: var(--large-body-font-size);
}

@media (max-width: 768px) {
  .grid-container {
    margin: 0 0 var(--margin-big);
    grid-template-columns: 1fr; /* First column fixed, second flexible */
    grid-template-rows: auto auto auto auto; /* Header + 3 cards rows */
    grid-template-areas:
    "header"
    "content"
    "chart"
    "history"
    "placeholder";
    gap: 0;
  }

  .panel {
    display: none;
  }

  .header {
    margin-bottom: 0;
  }

  .placeholder {
    margin-top: 100px;
    padding: 0 var(--margin-big);
  }
}
</style>
