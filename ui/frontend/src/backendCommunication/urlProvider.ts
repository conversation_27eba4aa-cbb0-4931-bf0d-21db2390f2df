class UrlProvider {
  private static determineBackendIpAndPort() {
    if (process.env.NODE_ENV === 'production') {
      return window.location.host;
    }

    return 'localhost:3000';
  }

  public static getApiUrl() {
    const ipAndPort = this.determineBackendIpAndPort();
    return `http://${ipAndPort}/api`;
  }

  static getWebSocketUrl() {
    const ipAndPort = this.determineBackendIpAndPort();
    console.log(ipAndPort);
    return `ws://${ipAndPort}`;
  }
}

export default UrlProvider;