import axios from 'axios';
import UrlProvider from '@/backendCommunication/urlProvider';

const API_URL = UrlProvider.getApiUrl();

export const getFridgeContent = async () => {
  const response = await axios.get(`${API_URL}/fridge-inventory`);
  return response.data.data;
};

export const getActions = async (page = 1, limit = 100) => {
  const response = await axios.get(`${API_URL}/actions`, {params: {page, limit}});
  return response.data;
};

export const getActionsReport = async () => {
  const response = await axios.get(`${API_URL}/actions-report`);
  return response.data.data;
};