{"name": "fridge-inventory-app", "version": "1.0.0", "description": "Local server app with a web UI to visualize fridge inventory database.", "license": "ISC", "author": "<PERSON><PERSON><PERSON>", "scripts": {"postinstall": "npm run install:backend && npm run install:frontend", "install:backend": "cd backend && npm install", "install:frontend": "cd frontend && npm install", "start": "npm run build:frontend && npm run build:backend && npm run start:backend", "build:frontend": "cd frontend && npm run build", "build:backend": "cd backend && npm run build", "start:backend": "cd backend && npm run start", "dev": "concurrently \"npm run dev:frontend\" \"npm run dev:backend\"", "dev:frontend": "cd frontend && npm run serve", "dev:backend": "cd backend && npm run dev"}, "devDependencies": {"concurrently": "^9.1.2"}, "dependencies": {"typescript": "^5.8.2"}}