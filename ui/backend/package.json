{"name": "fridge-inventory-app-backend", "version": "1.0.0", "main": "index.js", "scripts": {"build": "tsc", "start": "cross-env NODE_ENV=production node dist/index.js", "dev": "nodemon --exec ts-node src/index.ts"}, "author": "<PERSON><PERSON><PERSON>", "license": "ISC", "description": "", "dependencies": {"body-parser": "^1.20.3", "cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^4.21.2", "socket.io": "^4.8.1", "sqlite3": "^5.1.7"}, "devDependencies": {"@types/cors": "^2.8.17", "@types/express": "^5.0.0", "@types/sqlite3": "^3.1.11", "concurrently": "^9.1.2", "cross-env": "^7.0.3", "nodemon": "^3.1.9", "ts-node": "^10.9.2", "typescript": "^5.7.3"}}