import sqlite3 from 'sqlite3';

const db = new sqlite3.Database('../data.db');

// Create tables (if not exists)
db.serialize(() => {
  db.run(`
      CREATE TABLE IF NOT EXISTS actions
      (
          id
          INTEGER
          PRIMARY
          KEY
          AUTOINCREMENT,
          timestamp
          TEXT,
          action_type
          TEXT
          CHECK (
          action_type
          IN
      (
          'in',
          'out'
      )) NOT NULL,
          item_class TEXT NOT NULL
          )
  `);

  db.run(`
      CREATE TABLE IF NOT EXISTS fridge_inventory
      (
          item_class
          TEXT
          PRIMARY
          KEY,
          quantity
          INTEGER
          NOT
          NULL
      )
  `);
});

// Function to fetch fridge inventory
export const getFridgeInventory = () => {
  return new Promise<any[]>((resolve, reject) => {
    db.all('SELECT item_class, quantity FROM fridge_inventory', [], (err, rows) => {
      if (err) {
        reject({error: 'Database error', details: err.message});
      } else {
        resolve(rows);
      }
    });
  });
};

// Function to fetch actions with pagination
export const getActions = (page: number, limit: number) => {
  const offset = (page - 1) * limit;
  return new Promise<any[]>((resolve, reject) => {
    db.all(
      'SELECT timestamp, item_class, action_type FROM actions ORDER BY timestamp DESC LIMIT ? OFFSET ?',
      [limit, offset],
      (err, rows) => {
        if (err) {
          reject({error: 'Database error', details: err.message});
        } else {
          resolve(rows);
        }
      }
    );
  });
};

// Function to get total count of actions
export const getActionsCount = () => {
  return new Promise<number>((resolve, reject) => {
    db.get('SELECT COUNT(*) AS total FROM actions', [], (err, countResult) => {
      if (err) {
        reject({error: 'Database error', details: err.message});
      } else {
        let total = (countResult as { total: number })?.total;
        resolve(total || 0);
      }
    });
  });
};

// Function to fetch the number of actions for each item_class in the last 30 days
export const getActionsReport = () => {
  return new Promise<any[]>((resolve, reject) => {
    db.all(
      `SELECT item_class, COUNT(*) as actions 
       FROM actions 
       WHERE timestamp >= datetime('now', '-30 days') 
       GROUP BY item_class`,
      [],
      (err, rows) => {
        if (err) {
          reject({ error: 'Database error', details: err.message });
        } else {
          resolve(rows);
        }
      }
    );
  });
};

export const getLastActionId = () => {
  return new Promise<number>((resolve, reject) => {
    db.get('SELECT id FROM actions ORDER BY timestamp DESC LIMIT 1', [], (err, row) => {
      if (err) {
        reject({ error: 'Database error', details: err.message });
      } else {
        // If there's a result, return the id, otherwise return null
        // @ts-ignore
        resolve(row ? row.id : null);
      }
    });
  });
};

export default db;