import cors from 'cors';
import bodyParser from 'body-parser';
import dotenv from 'dotenv';
import express from 'express';
import {Server} from 'socket.io';
import path from 'path';
import routes from './routes';
import {getLocalIp} from './ipHelper';
import {setupNewDataNotification} from './webSocketHelper';

const mode = process.env.NODE_ENV || 'development';
const envFilePath = `.env.${mode}`;
dotenv.config({path: envFilePath});

const app = express();

console.log(process.env.NODE_ENV);

// CORS setup for development mode
if (process.env.NODE_ENV !== 'production') {
  app.use(cors({origin: process.env.FRONTEND_URL}));
}

app.use(bodyParser.json()); // Middleware for parsing JSON
app.use('/api', routes);    // Use API routes

const IP = process.env.NODE_ENV === 'production' ? getLocalIp() : 'localhost';
const PORT = parseInt(process.env.PORT || '3000', 10);

// Serve frontend static files in production mode
if (process.env.NODE_ENV === 'production') {
  const frontendPath = path.join(__dirname, '../../frontend/dist');
  app.use(express.static(frontendPath));
  app.get('*', (req, res) => {
    res.sendFile(path.join(frontendPath, 'index.html'));
  });
}

// Launch server
const expressServer = app.listen(PORT, IP, () => {
  const url = `http://${IP}:${PORT}`;
  console.log(`Backend running at ${url}`);
});

// Setup web socket
const io = new Server(expressServer, {
  cors: {
    origin: ['http://localhost:3000', 'http://localhost:8080']
  }
});

setupNewDataNotification(io);



