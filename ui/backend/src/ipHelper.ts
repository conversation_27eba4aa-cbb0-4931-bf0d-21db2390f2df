import os from 'os';

/**
 * Function to get the local backendCommunication IP (IPv4)
 * @returns {string} The local backendCommunication IP address or '0.0.0.0' if not found
 */
export function getLocalIp(): string {
  const interfaces = os.networkInterfaces();
  for (const key in interfaces) {
    for (const net of interfaces[key]!) {
      if (net.family === "IPv4" && !net.internal) return net.address;
    }
  }
  return '0.0.0.0';  // Fallback to 0.0.0.0 if no valid IP found
}