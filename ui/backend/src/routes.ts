import express from 'express';
import {getFridgeInventory, getActions, getActionsCount, getActionsReport} from './db';
import {getLocalIp} from './ipHelper';

const router = express.Router();

// Get all fridge content
router.get('/fridge-inventory', async (req, res) => {
  try {
    const rows = await getFridgeInventory();
    res.json({data: rows});
  } catch (err) {
    res.status(500).json(err);
  }
});

// Get paginated actions
router.get('/actions', async (req, res) => {
  let page = parseInt(req.query.page as string) || 1;
  let limit = parseInt(req.query.limit as string) || 100;

  try {
    const rows = await getActions(page, limit);
    const total = await getActionsCount();

    res.json({
      page,
      limit,
      total,
      totalPages: Math.ceil(total / limit),
      data: rows
    });
  } catch (err) {
    res.status(500).json(err);
  }
});

// Get action counts for the last 30 days by item
router.get('/actions-report', async (req, res) => {
  try {
    const rows = await getActionsReport();
    res.json({ data: rows });
  } catch (err) {
    res.status(500).json(err);
  }
});

// Get backend server details (IP & Port)
router.get('/server-info', (req, res) => {
  res.json({
    apiUrl: `http://${getLocalIp()}:${process.env.PORT || 3000}/api`
  });
});

export default router;