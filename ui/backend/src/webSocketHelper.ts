import {Server} from 'socket.io';
import {getLastActionId} from './db';

let savedLastActionId: number | null = null;

export const setupNewDataNotification = (io: Server) => {
  const notifyClients = () => {
    io.emit('new-data');
  };

  setInterval(async () => {
    const lastActionId = await getLastActionId();

    if (savedLastActionId !== lastActionId) {
      savedLastActionId = lastActionId;
      notifyClients();
    }
  }, 500);
};