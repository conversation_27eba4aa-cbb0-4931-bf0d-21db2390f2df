[build-system]
requires = ["flit_core>=3.4"]
build-backend = "flit_core.buildapi"

[project]
name = "fridge"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = "==3.10.17"
dependencies = [
  "numpy>=1.22.0",
  "opencv-python-headless>=*********",
  "ultralytics>=8.3.122",
  "glfw>=2.9.0",
  "imgui[glfw]>=2.0.0",
  # Only install on Linux/Windows x86_64
  "torch>=2.7.0 ; ((sys_platform == 'linux' and platform_machine != 'armv7l' and platform_machine != 'aarch64') or sys_platform == 'win32' or sys_platform == 'darwin')",
  "torchvision>=0.22.0 ; ((sys_platform == 'linux' and platform_machine != 'armv7l' and platform_machine != 'aarch64') or sys_platform == 'win32' or sys_platform == 'darwin')",
  "filedialpy>=1.3.3",
  "hailort>=4.21.0 ; sys_platform == 'linux' and platform_machine == 'aarch64'",
  "degirum>=0.17.2 ; sys_platform == 'linux' and platform_machine == 'aarch64'",
]


[[tool.uv.index]]
name = "pytorch-cu118"
url = "https://download.pytorch.org/whl/cu118"
explicit = true

[tool.uv.sources]
torch = [
  { index = "pytorch-cu118", marker = "(sys_platform == 'linux' or sys_platform == 'win32') and platform_machine != 'armv7l' and platform_machine != 'aarch64'" },
]
torchvision = [
  { index = "pytorch-cu118", marker = "(sys_platform == 'linux' or sys_platform == 'win32') and platform_machine != 'armv7l' and platform_machine != 'aarch64'" },
]
hailort = { path = "hailo/hailort-4.21.0-cp310-cp310-linux_aarch64.whl", marker = "sys_platform == 'linux' and platform_machine == 'aarch64'" }

[project.scripts]
app = "fridge.main:main"
