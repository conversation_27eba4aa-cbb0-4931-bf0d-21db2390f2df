[2025-07-26 13:02:45.646] [34008] [HailoRT] [info] [device.cpp:51] [Device] OS Version: Linux 6.12.20+rpt-rpi-2712 #1 SMP PREEMPT Debian 1:6.12.20-1+rpt1~bpo12+1 (2025-03-19) aarch64
[2025-07-26 13:02:45.647] [34008] [HailoRT] [info] [control.cpp:113] [control__parse_identify_results] firmware_version is: 4.21.0
[2025-07-26 13:02:45.647] [34008] [HailoRT] [info] [control.cpp:113] [control__parse_identify_results] firmware_version is: 4.21.0
[2025-07-26 13:02:45.722] [34008] [HailoRT] [info] [device.cpp:51] [Device] OS Version: Linux 6.12.20+rpt-rpi-2712 #1 SMP PREEMPT Debian 1:6.12.20-1+rpt1~bpo12+1 (2025-03-19) aarch64
[2025-07-26 13:02:45.722] [34008] [HailoRT] [info] [control.cpp:113] [control__parse_identify_results] firmware_version is: 4.21.0
[2025-07-26 13:02:45.723] [34008] [HailoRT] [info] [control.cpp:113] [control__parse_identify_results] firmware_version is: 4.21.0
[2025-07-26 13:02:45.835] [34016] [HailoRT] [info] [device.cpp:51] [Device] OS Version: Linux 6.12.20+rpt-rpi-2712 #1 SMP PREEMPT Debian 1:6.12.20-1+rpt1~bpo12+1 (2025-03-19) aarch64
[2025-07-26 13:02:45.835] [34016] [HailoRT] [info] [control.cpp:113] [control__parse_identify_results] firmware_version is: 4.21.0
[2025-07-26 13:02:45.835] [34016] [HailoRT] [info] [control.cpp:113] [control__parse_identify_results] firmware_version is: 4.21.0
[2025-07-26 13:02:45.887] [34016] [HailoRT] [info] [vdevice.cpp:535] [create] Creating vdevice with params: device_count: 1, scheduling_algorithm: ROUND_ROBIN, multi_process_service: true
[2025-07-26 13:02:45.939] [34016] [HailoRT] [info] [device.cpp:51] [Device] OS Version: Linux 6.12.20+rpt-rpi-2712 #1 SMP PREEMPT Debian 1:6.12.20-1+rpt1~bpo12+1 (2025-03-19) aarch64
[2025-07-26 13:02:45.939] [34016] [HailoRT] [info] [control.cpp:113] [control__parse_identify_results] firmware_version is: 4.21.0
[2025-07-26 13:02:45.946] [34016] [HailoRT] [info] [hef.cpp:1994] [get_network_group_and_network_name] No name was given. Addressing all networks of default network_group: yolov11n
[2025-07-26 13:02:45.946] [34016] [HailoRT] [info] [hef.cpp:1994] [get_network_group_and_network_name] No name was given. Addressing all networks of default network_group: yolov11n
[2025-07-26 13:02:46.085] [34016] [HailoRT] [info] [infer_model.cpp:417] [configure] Configuring network group 'yolov11n' with params: batch size: 8, power mode: ULTRA_PERFORMANCE, latency: NONE
[2025-07-26 13:02:46.087] [34016] [HailoRT] [info] [multi_io_elements.cpp:754] [create] Created (AsyncHwEl)
[2025-07-26 13:02:46.088] [34016] [HailoRT] [info] [queue_elements.cpp:450] [create] Created (EntryPushQEl0yolov11n/input_layer1 | timeout: 10s)
[2025-07-26 13:02:46.088] [34016] [HailoRT] [info] [filter_elements.cpp:101] [create] Created (PreInferEl1yolov11n/input_layer1 | Reorder - src_order: NHWC, src_shape: (640, 640, 3), dst_order: NHCW, dst_shape: (640, 640, 3))
[2025-07-26 13:02:46.088] [34016] [HailoRT] [info] [queue_elements.cpp:450] [create] Created (PushQEl1yolov11n/input_layer1 | timeout: 10s)
[2025-07-26 13:02:46.089] [34016] [HailoRT] [info] [multi_io_elements.cpp:135] [create] Created (NmsPPMuxEl0YOLOV8-Post-Process | Op YOLOV8, Name: YOLOV8-Post-Process, Score threshold: 0.300, IoU threshold: 0.60, Classes: 9, Max bboxes per class: 100, Image height: 640, Image width: 640)
[2025-07-26 13:02:46.115] [34016] [HailoRT] [info] [queue_elements.cpp:942] [create] Created (MultiPushQEl0YOLOV8-Post-Process | timeout: 10s)
[2025-07-26 13:02:46.115] [34016] [HailoRT] [info] [edge_elements.cpp:187] [create] Created (LastAsyncEl0NmsPPMuxEl0YOLOV8-Post-Process)
[2025-07-26 13:02:46.115] [34016] [HailoRT] [info] [pipeline.cpp:891] [print_deep_description] EntryPushQEl0yolov11n/input_layer1 | inputs: user | outputs: PreInferEl1yolov11n/input_layer1(running in thread_id: 34064)
[2025-07-26 13:02:46.115] [34016] [HailoRT] [info] [pipeline.cpp:891] [print_deep_description] PreInferEl1yolov11n/input_layer1 | inputs: EntryPushQEl0yolov11n/input_layer1[0] | outputs: PushQEl1yolov11n/input_layer1
[2025-07-26 13:02:46.115] [34016] [HailoRT] [info] [pipeline.cpp:891] [print_deep_description] PushQEl1yolov11n/input_layer1 | inputs: PreInferEl1yolov11n/input_layer1[0] | outputs: AsyncHwEl(running in thread_id: 34065)
[2025-07-26 13:02:46.115] [34016] [HailoRT] [info] [pipeline.cpp:891] [print_deep_description] AsyncHwEl | inputs: PushQEl1yolov11n/input_layer1[0] | outputs: MultiPushQEl0YOLOV8-Post-Process MultiPushQEl0YOLOV8-Post-Process MultiPushQEl0YOLOV8-Post-Process MultiPushQEl0YOLOV8-Post-Process MultiPushQEl0YOLOV8-Post-Process MultiPushQEl0YOLOV8-Post-Process
[2025-07-26 13:02:46.115] [34016] [HailoRT] [info] [pipeline.cpp:891] [print_deep_description] MultiPushQEl0YOLOV8-Post-Process | inputs: AsyncHwEl[0] AsyncHwEl[1] AsyncHwEl[2] AsyncHwEl[3] AsyncHwEl[4] AsyncHwEl[5] | outputs: NmsPPMuxEl0YOLOV8-Post-Process(running in thread_id: 34067)
[2025-07-26 13:02:46.115] [34016] [HailoRT] [info] [pipeline.cpp:891] [print_deep_description] NmsPPMuxEl0YOLOV8-Post-Process | inputs: MultiPushQEl0YOLOV8-Post-Process[0] | outputs: LastAsyncEl0NmsPPMuxEl0YOLOV8-Post-Process
[2025-07-26 13:02:46.115] [34016] [HailoRT] [info] [pipeline.cpp:891] [print_deep_description] LastAsyncEl0NmsPPMuxEl0YOLOV8-Post-Process | inputs: NmsPPMuxEl0YOLOV8-Post-Process[0] | outputs: user
[2025-07-26 13:02:46.115] [34016] [HailoRT] [info] [hef.cpp:1994] [get_network_group_and_network_name] No name was given. Addressing all networks of default network_group: yolov11n
[2025-07-26 13:02:46.115] [34016] [HailoRT] [info] [hef.cpp:1994] [get_network_group_and_network_name] No name was given. Addressing all networks of default network_group: yolov11n
[2025-07-26 13:02:46.321] [34008] [HailoRT] [info] [async_infer_runner.cpp:86] [shutdown] Pipeline was aborted. Shutting it down
[2025-07-26 13:02:46.322] [34008] [HailoRT] [info] [queue_elements.cpp:1131] [execute_deactivate] enqueue() in element MultiPushQEl0YOLOV8-Post-Process was aborted, got status = HAILO_SHUTDOWN_EVENT_SIGNALED(57)
[2025-07-26 13:02:46.322] [34008] [HailoRT] [info] [queue_elements.cpp:1131] [execute_deactivate] enqueue() in element MultiPushQEl0YOLOV8-Post-Process was aborted, got status = HAILO_SHUTDOWN_EVENT_SIGNALED(57)
[2025-07-26 13:02:46.322] [34008] [HailoRT] [info] [queue_elements.cpp:1131] [execute_deactivate] enqueue() in element MultiPushQEl0YOLOV8-Post-Process was aborted, got status = HAILO_SHUTDOWN_EVENT_SIGNALED(57)
[2025-07-26 13:02:46.322] [34008] [HailoRT] [info] [queue_elements.cpp:1131] [execute_deactivate] enqueue() in element MultiPushQEl0YOLOV8-Post-Process was aborted, got status = HAILO_SHUTDOWN_EVENT_SIGNALED(57)
[2025-07-26 13:02:46.322] [34008] [HailoRT] [info] [queue_elements.cpp:1131] [execute_deactivate] enqueue() in element MultiPushQEl0YOLOV8-Post-Process was aborted, got status = HAILO_SHUTDOWN_EVENT_SIGNALED(57)
[2025-07-26 13:02:46.322] [34008] [HailoRT] [info] [queue_elements.cpp:1131] [execute_deactivate] enqueue() in element MultiPushQEl0YOLOV8-Post-Process was aborted, got status = HAILO_SHUTDOWN_EVENT_SIGNALED(57)
[2025-07-26 13:02:46.322] [34008] [HailoRT] [info] [queue_elements.cpp:570] [execute_deactivate] enqueue() in element PushQEl1yolov11n/input_layer1 was aborted, got status = HAILO_SHUTDOWN_EVENT_SIGNALED(57)
[2025-07-26 13:02:46.322] [34008] [HailoRT] [info] [queue_elements.cpp:570] [execute_deactivate] enqueue() in element EntryPushQEl0yolov11n/input_layer1 was aborted, got status = HAILO_SHUTDOWN_EVENT_SIGNALED(57)
[2025-07-26 13:02:46.322] [34008] [HailoRT] [info] [queue_elements.cpp:46] [~BaseQueueElement] Queue element EntryPushQEl0yolov11n/input_layer1 has 0 frames in his Queue on destruction
[2025-07-26 13:02:46.322] [34008] [HailoRT] [info] [queue_elements.cpp:46] [~BaseQueueElement] Queue element PushQEl1yolov11n/input_layer1 has 0 frames in his Queue on destruction
[2025-07-26 13:02:46.323] [34062] [HailoRT] [info] [vdevice.cpp:436] [listener_run_in_thread] Shutdown event was signaled in listener_run_in_thread
[2025-07-26 13:10:26.786] [34226] [HailoRT] [info] [device.cpp:51] [Device] OS Version: Linux 6.12.20+rpt-rpi-2712 #1 SMP PREEMPT Debian 1:6.12.20-1+rpt1~bpo12+1 (2025-03-19) aarch64
[2025-07-26 13:10:26.786] [34226] [HailoRT] [info] [control.cpp:113] [control__parse_identify_results] firmware_version is: 4.21.0
[2025-07-26 13:10:26.786] [34226] [HailoRT] [info] [control.cpp:113] [control__parse_identify_results] firmware_version is: 4.21.0
[2025-07-26 13:10:26.834] [34226] [HailoRT] [info] [device.cpp:51] [Device] OS Version: Linux 6.12.20+rpt-rpi-2712 #1 SMP PREEMPT Debian 1:6.12.20-1+rpt1~bpo12+1 (2025-03-19) aarch64
[2025-07-26 13:10:26.834] [34226] [HailoRT] [info] [control.cpp:113] [control__parse_identify_results] firmware_version is: 4.21.0
[2025-07-26 13:10:26.835] [34226] [HailoRT] [info] [control.cpp:113] [control__parse_identify_results] firmware_version is: 4.21.0
[2025-07-26 13:10:26.958] [34234] [HailoRT] [info] [device.cpp:51] [Device] OS Version: Linux 6.12.20+rpt-rpi-2712 #1 SMP PREEMPT Debian 1:6.12.20-1+rpt1~bpo12+1 (2025-03-19) aarch64
[2025-07-26 13:10:26.959] [34234] [HailoRT] [info] [control.cpp:113] [control__parse_identify_results] firmware_version is: 4.21.0
[2025-07-26 13:10:26.959] [34234] [HailoRT] [info] [control.cpp:113] [control__parse_identify_results] firmware_version is: 4.21.0
[2025-07-26 13:10:27.003] [34234] [HailoRT] [info] [vdevice.cpp:535] [create] Creating vdevice with params: device_count: 1, scheduling_algorithm: ROUND_ROBIN, multi_process_service: true
[2025-07-26 13:10:27.018] [34234] [HailoRT] [info] [device.cpp:51] [Device] OS Version: Linux 6.12.20+rpt-rpi-2712 #1 SMP PREEMPT Debian 1:6.12.20-1+rpt1~bpo12+1 (2025-03-19) aarch64
[2025-07-26 13:10:27.018] [34234] [HailoRT] [info] [control.cpp:113] [control__parse_identify_results] firmware_version is: 4.21.0
[2025-07-26 13:10:27.024] [34234] [HailoRT] [info] [hef.cpp:1994] [get_network_group_and_network_name] No name was given. Addressing all networks of default network_group: yolov11n
[2025-07-26 13:10:27.024] [34234] [HailoRT] [info] [hef.cpp:1994] [get_network_group_and_network_name] No name was given. Addressing all networks of default network_group: yolov11n
[2025-07-26 13:10:27.150] [34234] [HailoRT] [info] [infer_model.cpp:417] [configure] Configuring network group 'yolov11n' with params: batch size: 8, power mode: ULTRA_PERFORMANCE, latency: NONE
[2025-07-26 13:10:27.152] [34234] [HailoRT] [info] [multi_io_elements.cpp:754] [create] Created (AsyncHwEl)
[2025-07-26 13:10:27.152] [34234] [HailoRT] [info] [queue_elements.cpp:450] [create] Created (EntryPushQEl0yolov11n/input_layer1 | timeout: 10s)
[2025-07-26 13:10:27.152] [34234] [HailoRT] [info] [filter_elements.cpp:101] [create] Created (PreInferEl1yolov11n/input_layer1 | Reorder - src_order: NHWC, src_shape: (640, 640, 3), dst_order: NHCW, dst_shape: (640, 640, 3))
[2025-07-26 13:10:27.152] [34234] [HailoRT] [info] [queue_elements.cpp:450] [create] Created (PushQEl1yolov11n/input_layer1 | timeout: 10s)
[2025-07-26 13:10:27.153] [34234] [HailoRT] [info] [multi_io_elements.cpp:135] [create] Created (NmsPPMuxEl0YOLOV8-Post-Process | Op YOLOV8, Name: YOLOV8-Post-Process, Score threshold: 0.300, IoU threshold: 0.60, Classes: 9, Max bboxes per class: 100, Image height: 640, Image width: 640)
[2025-07-26 13:10:27.179] [34234] [HailoRT] [info] [queue_elements.cpp:942] [create] Created (MultiPushQEl0YOLOV8-Post-Process | timeout: 10s)
[2025-07-26 13:10:27.179] [34234] [HailoRT] [info] [edge_elements.cpp:187] [create] Created (LastAsyncEl0NmsPPMuxEl0YOLOV8-Post-Process)
[2025-07-26 13:10:27.179] [34234] [HailoRT] [info] [pipeline.cpp:891] [print_deep_description] EntryPushQEl0yolov11n/input_layer1 | inputs: user | outputs: PreInferEl1yolov11n/input_layer1(running in thread_id: 34272)
[2025-07-26 13:10:27.179] [34234] [HailoRT] [info] [pipeline.cpp:891] [print_deep_description] PreInferEl1yolov11n/input_layer1 | inputs: EntryPushQEl0yolov11n/input_layer1[0] | outputs: PushQEl1yolov11n/input_layer1
[2025-07-26 13:10:27.179] [34234] [HailoRT] [info] [pipeline.cpp:891] [print_deep_description] PushQEl1yolov11n/input_layer1 | inputs: PreInferEl1yolov11n/input_layer1[0] | outputs: AsyncHwEl(running in thread_id: 34273)
[2025-07-26 13:10:27.179] [34234] [HailoRT] [info] [pipeline.cpp:891] [print_deep_description] AsyncHwEl | inputs: PushQEl1yolov11n/input_layer1[0] | outputs: MultiPushQEl0YOLOV8-Post-Process MultiPushQEl0YOLOV8-Post-Process MultiPushQEl0YOLOV8-Post-Process MultiPushQEl0YOLOV8-Post-Process MultiPushQEl0YOLOV8-Post-Process MultiPushQEl0YOLOV8-Post-Process
[2025-07-26 13:10:27.179] [34234] [HailoRT] [info] [pipeline.cpp:891] [print_deep_description] MultiPushQEl0YOLOV8-Post-Process | inputs: AsyncHwEl[0] AsyncHwEl[1] AsyncHwEl[2] AsyncHwEl[3] AsyncHwEl[4] AsyncHwEl[5] | outputs: NmsPPMuxEl0YOLOV8-Post-Process(running in thread_id: 34274)
[2025-07-26 13:10:27.179] [34234] [HailoRT] [info] [pipeline.cpp:891] [print_deep_description] NmsPPMuxEl0YOLOV8-Post-Process | inputs: MultiPushQEl0YOLOV8-Post-Process[0] | outputs: LastAsyncEl0NmsPPMuxEl0YOLOV8-Post-Process
[2025-07-26 13:10:27.179] [34234] [HailoRT] [info] [pipeline.cpp:891] [print_deep_description] LastAsyncEl0NmsPPMuxEl0YOLOV8-Post-Process | inputs: NmsPPMuxEl0YOLOV8-Post-Process[0] | outputs: user
[2025-07-26 13:10:27.179] [34234] [HailoRT] [info] [hef.cpp:1994] [get_network_group_and_network_name] No name was given. Addressing all networks of default network_group: yolov11n
[2025-07-26 13:10:27.179] [34234] [HailoRT] [info] [hef.cpp:1994] [get_network_group_and_network_name] No name was given. Addressing all networks of default network_group: yolov11n
[2025-07-26 13:10:27.386] [34226] [HailoRT] [info] [async_infer_runner.cpp:86] [shutdown] Pipeline was aborted. Shutting it down
[2025-07-26 13:10:27.387] [34226] [HailoRT] [info] [queue_elements.cpp:1131] [execute_deactivate] enqueue() in element MultiPushQEl0YOLOV8-Post-Process was aborted, got status = HAILO_SHUTDOWN_EVENT_SIGNALED(57)
[2025-07-26 13:10:27.387] [34226] [HailoRT] [info] [queue_elements.cpp:1131] [execute_deactivate] enqueue() in element MultiPushQEl0YOLOV8-Post-Process was aborted, got status = HAILO_SHUTDOWN_EVENT_SIGNALED(57)
[2025-07-26 13:10:27.387] [34226] [HailoRT] [info] [queue_elements.cpp:1131] [execute_deactivate] enqueue() in element MultiPushQEl0YOLOV8-Post-Process was aborted, got status = HAILO_SHUTDOWN_EVENT_SIGNALED(57)
[2025-07-26 13:10:27.387] [34226] [HailoRT] [info] [queue_elements.cpp:1131] [execute_deactivate] enqueue() in element MultiPushQEl0YOLOV8-Post-Process was aborted, got status = HAILO_SHUTDOWN_EVENT_SIGNALED(57)
[2025-07-26 13:10:27.387] [34226] [HailoRT] [info] [queue_elements.cpp:1131] [execute_deactivate] enqueue() in element MultiPushQEl0YOLOV8-Post-Process was aborted, got status = HAILO_SHUTDOWN_EVENT_SIGNALED(57)
[2025-07-26 13:10:27.387] [34226] [HailoRT] [info] [queue_elements.cpp:1131] [execute_deactivate] enqueue() in element MultiPushQEl0YOLOV8-Post-Process was aborted, got status = HAILO_SHUTDOWN_EVENT_SIGNALED(57)
[2025-07-26 13:10:27.387] [34226] [HailoRT] [info] [queue_elements.cpp:570] [execute_deactivate] enqueue() in element PushQEl1yolov11n/input_layer1 was aborted, got status = HAILO_SHUTDOWN_EVENT_SIGNALED(57)
[2025-07-26 13:10:27.387] [34226] [HailoRT] [info] [queue_elements.cpp:570] [execute_deactivate] enqueue() in element EntryPushQEl0yolov11n/input_layer1 was aborted, got status = HAILO_SHUTDOWN_EVENT_SIGNALED(57)
[2025-07-26 13:10:27.387] [34226] [HailoRT] [info] [queue_elements.cpp:46] [~BaseQueueElement] Queue element EntryPushQEl0yolov11n/input_layer1 has 0 frames in his Queue on destruction
[2025-07-26 13:10:27.387] [34226] [HailoRT] [info] [queue_elements.cpp:46] [~BaseQueueElement] Queue element PushQEl1yolov11n/input_layer1 has 0 frames in his Queue on destruction
[2025-07-26 13:10:27.389] [34269] [HailoRT] [info] [vdevice.cpp:436] [listener_run_in_thread] Shutdown event was signaled in listener_run_in_thread
[2025-07-26 13:10:51.147] [34319] [HailoRT] [info] [device.cpp:51] [Device] OS Version: Linux 6.12.20+rpt-rpi-2712 #1 SMP PREEMPT Debian 1:6.12.20-1+rpt1~bpo12+1 (2025-03-19) aarch64
[2025-07-26 13:10:51.147] [34319] [HailoRT] [info] [control.cpp:113] [control__parse_identify_results] firmware_version is: 4.21.0
[2025-07-26 13:10:51.147] [34319] [HailoRT] [info] [control.cpp:113] [control__parse_identify_results] firmware_version is: 4.21.0
[2025-07-26 13:10:51.210] [34319] [HailoRT] [info] [device.cpp:51] [Device] OS Version: Linux 6.12.20+rpt-rpi-2712 #1 SMP PREEMPT Debian 1:6.12.20-1+rpt1~bpo12+1 (2025-03-19) aarch64
[2025-07-26 13:10:51.210] [34319] [HailoRT] [info] [control.cpp:113] [control__parse_identify_results] firmware_version is: 4.21.0
[2025-07-26 13:10:51.211] [34319] [HailoRT] [info] [control.cpp:113] [control__parse_identify_results] firmware_version is: 4.21.0
[2025-07-26 13:10:51.330] [34327] [HailoRT] [info] [device.cpp:51] [Device] OS Version: Linux 6.12.20+rpt-rpi-2712 #1 SMP PREEMPT Debian 1:6.12.20-1+rpt1~bpo12+1 (2025-03-19) aarch64
[2025-07-26 13:10:51.331] [34327] [HailoRT] [info] [control.cpp:113] [control__parse_identify_results] firmware_version is: 4.21.0
[2025-07-26 13:10:51.331] [34327] [HailoRT] [info] [control.cpp:113] [control__parse_identify_results] firmware_version is: 4.21.0
[2025-07-26 13:10:51.375] [34327] [HailoRT] [info] [vdevice.cpp:535] [create] Creating vdevice with params: device_count: 1, scheduling_algorithm: ROUND_ROBIN, multi_process_service: true
[2025-07-26 13:10:51.388] [34327] [HailoRT] [info] [device.cpp:51] [Device] OS Version: Linux 6.12.20+rpt-rpi-2712 #1 SMP PREEMPT Debian 1:6.12.20-1+rpt1~bpo12+1 (2025-03-19) aarch64
[2025-07-26 13:10:51.388] [34327] [HailoRT] [info] [control.cpp:113] [control__parse_identify_results] firmware_version is: 4.21.0
[2025-07-26 13:10:51.394] [34327] [HailoRT] [info] [hef.cpp:1994] [get_network_group_and_network_name] No name was given. Addressing all networks of default network_group: yolov11n
[2025-07-26 13:10:51.394] [34327] [HailoRT] [info] [hef.cpp:1994] [get_network_group_and_network_name] No name was given. Addressing all networks of default network_group: yolov11n
[2025-07-26 13:10:51.523] [34327] [HailoRT] [info] [infer_model.cpp:417] [configure] Configuring network group 'yolov11n' with params: batch size: 8, power mode: ULTRA_PERFORMANCE, latency: NONE
[2025-07-26 13:10:51.523] [34327] [HailoRT] [info] [multi_io_elements.cpp:754] [create] Created (AsyncHwEl)
[2025-07-26 13:10:51.524] [34327] [HailoRT] [info] [queue_elements.cpp:450] [create] Created (EntryPushQEl0yolov11n/input_layer1 | timeout: 10s)
[2025-07-26 13:10:51.524] [34327] [HailoRT] [info] [filter_elements.cpp:101] [create] Created (PreInferEl1yolov11n/input_layer1 | Reorder - src_order: NHWC, src_shape: (640, 640, 3), dst_order: NHCW, dst_shape: (640, 640, 3))
[2025-07-26 13:10:51.524] [34327] [HailoRT] [info] [queue_elements.cpp:450] [create] Created (PushQEl1yolov11n/input_layer1 | timeout: 10s)
[2025-07-26 13:10:51.525] [34327] [HailoRT] [info] [multi_io_elements.cpp:135] [create] Created (NmsPPMuxEl0YOLOV8-Post-Process | Op YOLOV8, Name: YOLOV8-Post-Process, Score threshold: 0.300, IoU threshold: 0.60, Classes: 9, Max bboxes per class: 100, Image height: 640, Image width: 640)
[2025-07-26 13:10:51.555] [34327] [HailoRT] [info] [queue_elements.cpp:942] [create] Created (MultiPushQEl0YOLOV8-Post-Process | timeout: 10s)
[2025-07-26 13:10:51.555] [34327] [HailoRT] [info] [edge_elements.cpp:187] [create] Created (LastAsyncEl0NmsPPMuxEl0YOLOV8-Post-Process)
[2025-07-26 13:10:51.555] [34327] [HailoRT] [info] [pipeline.cpp:891] [print_deep_description] EntryPushQEl0yolov11n/input_layer1 | inputs: user | outputs: PreInferEl1yolov11n/input_layer1(running in thread_id: 34374)
[2025-07-26 13:10:51.555] [34327] [HailoRT] [info] [pipeline.cpp:891] [print_deep_description] PreInferEl1yolov11n/input_layer1 | inputs: EntryPushQEl0yolov11n/input_layer1[0] | outputs: PushQEl1yolov11n/input_layer1
[2025-07-26 13:10:51.555] [34327] [HailoRT] [info] [pipeline.cpp:891] [print_deep_description] PushQEl1yolov11n/input_layer1 | inputs: PreInferEl1yolov11n/input_layer1[0] | outputs: AsyncHwEl(running in thread_id: 34375)
[2025-07-26 13:10:51.555] [34327] [HailoRT] [info] [pipeline.cpp:891] [print_deep_description] AsyncHwEl | inputs: PushQEl1yolov11n/input_layer1[0] | outputs: MultiPushQEl0YOLOV8-Post-Process MultiPushQEl0YOLOV8-Post-Process MultiPushQEl0YOLOV8-Post-Process MultiPushQEl0YOLOV8-Post-Process MultiPushQEl0YOLOV8-Post-Process MultiPushQEl0YOLOV8-Post-Process
[2025-07-26 13:10:51.555] [34327] [HailoRT] [info] [pipeline.cpp:891] [print_deep_description] MultiPushQEl0YOLOV8-Post-Process | inputs: AsyncHwEl[0] AsyncHwEl[1] AsyncHwEl[2] AsyncHwEl[3] AsyncHwEl[4] AsyncHwEl[5] | outputs: NmsPPMuxEl0YOLOV8-Post-Process(running in thread_id: 34376)
[2025-07-26 13:10:51.555] [34327] [HailoRT] [info] [pipeline.cpp:891] [print_deep_description] NmsPPMuxEl0YOLOV8-Post-Process | inputs: MultiPushQEl0YOLOV8-Post-Process[0] | outputs: LastAsyncEl0NmsPPMuxEl0YOLOV8-Post-Process
[2025-07-26 13:10:51.555] [34327] [HailoRT] [info] [pipeline.cpp:891] [print_deep_description] LastAsyncEl0NmsPPMuxEl0YOLOV8-Post-Process | inputs: NmsPPMuxEl0YOLOV8-Post-Process[0] | outputs: user
[2025-07-26 13:10:51.555] [34327] [HailoRT] [info] [hef.cpp:1994] [get_network_group_and_network_name] No name was given. Addressing all networks of default network_group: yolov11n
[2025-07-26 13:10:51.555] [34327] [HailoRT] [info] [hef.cpp:1994] [get_network_group_and_network_name] No name was given. Addressing all networks of default network_group: yolov11n
[2025-07-26 13:10:51.755] [34319] [HailoRT] [info] [async_infer_runner.cpp:86] [shutdown] Pipeline was aborted. Shutting it down
[2025-07-26 13:10:51.756] [34319] [HailoRT] [info] [queue_elements.cpp:1131] [execute_deactivate] enqueue() in element MultiPushQEl0YOLOV8-Post-Process was aborted, got status = HAILO_SHUTDOWN_EVENT_SIGNALED(57)
[2025-07-26 13:10:51.756] [34319] [HailoRT] [info] [queue_elements.cpp:1131] [execute_deactivate] enqueue() in element MultiPushQEl0YOLOV8-Post-Process was aborted, got status = HAILO_SHUTDOWN_EVENT_SIGNALED(57)
[2025-07-26 13:10:51.756] [34319] [HailoRT] [info] [queue_elements.cpp:1131] [execute_deactivate] enqueue() in element MultiPushQEl0YOLOV8-Post-Process was aborted, got status = HAILO_SHUTDOWN_EVENT_SIGNALED(57)
[2025-07-26 13:10:51.756] [34319] [HailoRT] [info] [queue_elements.cpp:1131] [execute_deactivate] enqueue() in element MultiPushQEl0YOLOV8-Post-Process was aborted, got status = HAILO_SHUTDOWN_EVENT_SIGNALED(57)
[2025-07-26 13:10:51.756] [34319] [HailoRT] [info] [queue_elements.cpp:1131] [execute_deactivate] enqueue() in element MultiPushQEl0YOLOV8-Post-Process was aborted, got status = HAILO_SHUTDOWN_EVENT_SIGNALED(57)
[2025-07-26 13:10:51.756] [34319] [HailoRT] [info] [queue_elements.cpp:1131] [execute_deactivate] enqueue() in element MultiPushQEl0YOLOV8-Post-Process was aborted, got status = HAILO_SHUTDOWN_EVENT_SIGNALED(57)
[2025-07-26 13:10:51.756] [34319] [HailoRT] [info] [queue_elements.cpp:570] [execute_deactivate] enqueue() in element PushQEl1yolov11n/input_layer1 was aborted, got status = HAILO_SHUTDOWN_EVENT_SIGNALED(57)
[2025-07-26 13:10:51.756] [34319] [HailoRT] [info] [queue_elements.cpp:570] [execute_deactivate] enqueue() in element EntryPushQEl0yolov11n/input_layer1 was aborted, got status = HAILO_SHUTDOWN_EVENT_SIGNALED(57)
[2025-07-26 13:10:51.756] [34319] [HailoRT] [info] [queue_elements.cpp:46] [~BaseQueueElement] Queue element EntryPushQEl0yolov11n/input_layer1 has 0 frames in his Queue on destruction
[2025-07-26 13:10:51.756] [34319] [HailoRT] [info] [queue_elements.cpp:46] [~BaseQueueElement] Queue element PushQEl1yolov11n/input_layer1 has 0 frames in his Queue on destruction
[2025-07-26 13:10:51.757] [34372] [HailoRT] [info] [vdevice.cpp:436] [listener_run_in_thread] Shutdown event was signaled in listener_run_in_thread
[2025-07-26 13:11:42.015] [34425] [HailoRT] [info] [device.cpp:51] [Device] OS Version: Linux 6.12.20+rpt-rpi-2712 #1 SMP PREEMPT Debian 1:6.12.20-1+rpt1~bpo12+1 (2025-03-19) aarch64
[2025-07-26 13:11:42.015] [34425] [HailoRT] [info] [control.cpp:113] [control__parse_identify_results] firmware_version is: 4.21.0
[2025-07-26 13:11:42.015] [34425] [HailoRT] [info] [control.cpp:113] [control__parse_identify_results] firmware_version is: 4.21.0
[2025-07-26 13:11:42.078] [34425] [HailoRT] [info] [device.cpp:51] [Device] OS Version: Linux 6.12.20+rpt-rpi-2712 #1 SMP PREEMPT Debian 1:6.12.20-1+rpt1~bpo12+1 (2025-03-19) aarch64
[2025-07-26 13:11:42.078] [34425] [HailoRT] [info] [control.cpp:113] [control__parse_identify_results] firmware_version is: 4.21.0
[2025-07-26 13:11:42.078] [34425] [HailoRT] [info] [control.cpp:113] [control__parse_identify_results] firmware_version is: 4.21.0
[2025-07-26 13:11:42.185] [34433] [HailoRT] [info] [device.cpp:51] [Device] OS Version: Linux 6.12.20+rpt-rpi-2712 #1 SMP PREEMPT Debian 1:6.12.20-1+rpt1~bpo12+1 (2025-03-19) aarch64
[2025-07-26 13:11:42.186] [34433] [HailoRT] [info] [control.cpp:113] [control__parse_identify_results] firmware_version is: 4.21.0
[2025-07-26 13:11:42.186] [34433] [HailoRT] [info] [control.cpp:113] [control__parse_identify_results] firmware_version is: 4.21.0
[2025-07-26 13:11:42.211] [34433] [HailoRT] [info] [vdevice.cpp:535] [create] Creating vdevice with params: device_count: 1, scheduling_algorithm: ROUND_ROBIN, multi_process_service: true
[2025-07-26 13:11:42.223] [34433] [HailoRT] [info] [device.cpp:51] [Device] OS Version: Linux 6.12.20+rpt-rpi-2712 #1 SMP PREEMPT Debian 1:6.12.20-1+rpt1~bpo12+1 (2025-03-19) aarch64
[2025-07-26 13:11:42.224] [34433] [HailoRT] [info] [control.cpp:113] [control__parse_identify_results] firmware_version is: 4.21.0
[2025-07-26 13:11:42.229] [34433] [HailoRT] [info] [hef.cpp:1994] [get_network_group_and_network_name] No name was given. Addressing all networks of default network_group: yolov11n
[2025-07-26 13:11:42.230] [34433] [HailoRT] [info] [hef.cpp:1994] [get_network_group_and_network_name] No name was given. Addressing all networks of default network_group: yolov11n
[2025-07-26 13:11:42.362] [34433] [HailoRT] [info] [infer_model.cpp:417] [configure] Configuring network group 'yolov11n' with params: batch size: 8, power mode: ULTRA_PERFORMANCE, latency: NONE
[2025-07-26 13:11:42.363] [34433] [HailoRT] [info] [multi_io_elements.cpp:754] [create] Created (AsyncHwEl)
[2025-07-26 13:11:42.364] [34433] [HailoRT] [info] [queue_elements.cpp:450] [create] Created (EntryPushQEl0yolov11n/input_layer1 | timeout: 10s)
[2025-07-26 13:11:42.364] [34433] [HailoRT] [info] [filter_elements.cpp:101] [create] Created (PreInferEl1yolov11n/input_layer1 | Reorder - src_order: NHWC, src_shape: (640, 640, 3), dst_order: NHCW, dst_shape: (640, 640, 3))
[2025-07-26 13:11:42.364] [34433] [HailoRT] [info] [queue_elements.cpp:450] [create] Created (PushQEl1yolov11n/input_layer1 | timeout: 10s)
[2025-07-26 13:11:42.365] [34433] [HailoRT] [info] [multi_io_elements.cpp:135] [create] Created (NmsPPMuxEl0YOLOV8-Post-Process | Op YOLOV8, Name: YOLOV8-Post-Process, Score threshold: 0.300, IoU threshold: 0.60, Classes: 9, Max bboxes per class: 100, Image height: 640, Image width: 640)
[2025-07-26 13:11:42.395] [34433] [HailoRT] [info] [queue_elements.cpp:942] [create] Created (MultiPushQEl0YOLOV8-Post-Process | timeout: 10s)
[2025-07-26 13:11:42.395] [34433] [HailoRT] [info] [edge_elements.cpp:187] [create] Created (LastAsyncEl0NmsPPMuxEl0YOLOV8-Post-Process)
[2025-07-26 13:11:42.395] [34433] [HailoRT] [info] [pipeline.cpp:891] [print_deep_description] EntryPushQEl0yolov11n/input_layer1 | inputs: user | outputs: PreInferEl1yolov11n/input_layer1(running in thread_id: 34481)
[2025-07-26 13:11:42.395] [34433] [HailoRT] [info] [pipeline.cpp:891] [print_deep_description] PreInferEl1yolov11n/input_layer1 | inputs: EntryPushQEl0yolov11n/input_layer1[0] | outputs: PushQEl1yolov11n/input_layer1
[2025-07-26 13:11:42.395] [34433] [HailoRT] [info] [pipeline.cpp:891] [print_deep_description] PushQEl1yolov11n/input_layer1 | inputs: PreInferEl1yolov11n/input_layer1[0] | outputs: AsyncHwEl(running in thread_id: 34482)
[2025-07-26 13:11:42.395] [34433] [HailoRT] [info] [pipeline.cpp:891] [print_deep_description] AsyncHwEl | inputs: PushQEl1yolov11n/input_layer1[0] | outputs: MultiPushQEl0YOLOV8-Post-Process MultiPushQEl0YOLOV8-Post-Process MultiPushQEl0YOLOV8-Post-Process MultiPushQEl0YOLOV8-Post-Process MultiPushQEl0YOLOV8-Post-Process MultiPushQEl0YOLOV8-Post-Process
[2025-07-26 13:11:42.395] [34433] [HailoRT] [info] [pipeline.cpp:891] [print_deep_description] MultiPushQEl0YOLOV8-Post-Process | inputs: AsyncHwEl[0] AsyncHwEl[1] AsyncHwEl[2] AsyncHwEl[3] AsyncHwEl[4] AsyncHwEl[5] | outputs: NmsPPMuxEl0YOLOV8-Post-Process(running in thread_id: 34483)
[2025-07-26 13:11:42.395] [34433] [HailoRT] [info] [pipeline.cpp:891] [print_deep_description] NmsPPMuxEl0YOLOV8-Post-Process | inputs: MultiPushQEl0YOLOV8-Post-Process[0] | outputs: LastAsyncEl0NmsPPMuxEl0YOLOV8-Post-Process
[2025-07-26 13:11:42.395] [34433] [HailoRT] [info] [pipeline.cpp:891] [print_deep_description] LastAsyncEl0NmsPPMuxEl0YOLOV8-Post-Process | inputs: NmsPPMuxEl0YOLOV8-Post-Process[0] | outputs: user
[2025-07-26 13:11:42.396] [34433] [HailoRT] [info] [hef.cpp:1994] [get_network_group_and_network_name] No name was given. Addressing all networks of default network_group: yolov11n
[2025-07-26 13:11:42.396] [34433] [HailoRT] [info] [hef.cpp:1994] [get_network_group_and_network_name] No name was given. Addressing all networks of default network_group: yolov11n
[2025-07-26 13:11:42.596] [34425] [HailoRT] [info] [async_infer_runner.cpp:86] [shutdown] Pipeline was aborted. Shutting it down
[2025-07-26 13:11:42.597] [34425] [HailoRT] [info] [queue_elements.cpp:1131] [execute_deactivate] enqueue() in element MultiPushQEl0YOLOV8-Post-Process was aborted, got status = HAILO_SHUTDOWN_EVENT_SIGNALED(57)
[2025-07-26 13:11:42.597] [34425] [HailoRT] [info] [queue_elements.cpp:1131] [execute_deactivate] enqueue() in element MultiPushQEl0YOLOV8-Post-Process was aborted, got status = HAILO_SHUTDOWN_EVENT_SIGNALED(57)
[2025-07-26 13:11:42.597] [34425] [HailoRT] [info] [queue_elements.cpp:1131] [execute_deactivate] enqueue() in element MultiPushQEl0YOLOV8-Post-Process was aborted, got status = HAILO_SHUTDOWN_EVENT_SIGNALED(57)
[2025-07-26 13:11:42.597] [34425] [HailoRT] [info] [queue_elements.cpp:1131] [execute_deactivate] enqueue() in element MultiPushQEl0YOLOV8-Post-Process was aborted, got status = HAILO_SHUTDOWN_EVENT_SIGNALED(57)
[2025-07-26 13:11:42.597] [34425] [HailoRT] [info] [queue_elements.cpp:1131] [execute_deactivate] enqueue() in element MultiPushQEl0YOLOV8-Post-Process was aborted, got status = HAILO_SHUTDOWN_EVENT_SIGNALED(57)
[2025-07-26 13:11:42.597] [34425] [HailoRT] [info] [queue_elements.cpp:1131] [execute_deactivate] enqueue() in element MultiPushQEl0YOLOV8-Post-Process was aborted, got status = HAILO_SHUTDOWN_EVENT_SIGNALED(57)
[2025-07-26 13:11:42.597] [34425] [HailoRT] [info] [queue_elements.cpp:570] [execute_deactivate] enqueue() in element PushQEl1yolov11n/input_layer1 was aborted, got status = HAILO_SHUTDOWN_EVENT_SIGNALED(57)
[2025-07-26 13:11:42.597] [34425] [HailoRT] [info] [queue_elements.cpp:570] [execute_deactivate] enqueue() in element EntryPushQEl0yolov11n/input_layer1 was aborted, got status = HAILO_SHUTDOWN_EVENT_SIGNALED(57)
[2025-07-26 13:11:42.597] [34425] [HailoRT] [info] [queue_elements.cpp:46] [~BaseQueueElement] Queue element EntryPushQEl0yolov11n/input_layer1 has 0 frames in his Queue on destruction
[2025-07-26 13:11:42.597] [34425] [HailoRT] [info] [queue_elements.cpp:46] [~BaseQueueElement] Queue element PushQEl1yolov11n/input_layer1 has 0 frames in his Queue on destruction
[2025-07-26 13:11:42.598] [34478] [HailoRT] [info] [vdevice.cpp:436] [listener_run_in_thread] Shutdown event was signaled in listener_run_in_thread
[2025-07-26 13:13:33.754] [34527] [HailoRT] [info] [device.cpp:51] [Device] OS Version: Linux 6.12.20+rpt-rpi-2712 #1 SMP PREEMPT Debian 1:6.12.20-1+rpt1~bpo12+1 (2025-03-19) aarch64
[2025-07-26 13:13:33.755] [34527] [HailoRT] [info] [control.cpp:113] [control__parse_identify_results] firmware_version is: 4.21.0
[2025-07-26 13:13:33.755] [34527] [HailoRT] [info] [control.cpp:113] [control__parse_identify_results] firmware_version is: 4.21.0
[2025-07-26 13:13:33.815] [34527] [HailoRT] [info] [device.cpp:51] [Device] OS Version: Linux 6.12.20+rpt-rpi-2712 #1 SMP PREEMPT Debian 1:6.12.20-1+rpt1~bpo12+1 (2025-03-19) aarch64
[2025-07-26 13:13:33.815] [34527] [HailoRT] [info] [control.cpp:113] [control__parse_identify_results] firmware_version is: 4.21.0
[2025-07-26 13:13:33.815] [34527] [HailoRT] [info] [control.cpp:113] [control__parse_identify_results] firmware_version is: 4.21.0
[2025-07-26 13:13:33.935] [34535] [HailoRT] [info] [device.cpp:51] [Device] OS Version: Linux 6.12.20+rpt-rpi-2712 #1 SMP PREEMPT Debian 1:6.12.20-1+rpt1~bpo12+1 (2025-03-19) aarch64
[2025-07-26 13:13:33.935] [34535] [HailoRT] [info] [control.cpp:113] [control__parse_identify_results] firmware_version is: 4.21.0
[2025-07-26 13:13:33.935] [34535] [HailoRT] [info] [control.cpp:113] [control__parse_identify_results] firmware_version is: 4.21.0
[2025-07-26 13:13:33.971] [34535] [HailoRT] [info] [vdevice.cpp:535] [create] Creating vdevice with params: device_count: 1, scheduling_algorithm: ROUND_ROBIN, multi_process_service: true
[2025-07-26 13:13:33.986] [34535] [HailoRT] [info] [device.cpp:51] [Device] OS Version: Linux 6.12.20+rpt-rpi-2712 #1 SMP PREEMPT Debian 1:6.12.20-1+rpt1~bpo12+1 (2025-03-19) aarch64
[2025-07-26 13:13:33.987] [34535] [HailoRT] [info] [control.cpp:113] [control__parse_identify_results] firmware_version is: 4.21.0
[2025-07-26 13:13:33.992] [34535] [HailoRT] [info] [hef.cpp:1994] [get_network_group_and_network_name] No name was given. Addressing all networks of default network_group: yolov11n
[2025-07-26 13:13:33.992] [34535] [HailoRT] [info] [hef.cpp:1994] [get_network_group_and_network_name] No name was given. Addressing all networks of default network_group: yolov11n
[2025-07-26 13:13:34.127] [34535] [HailoRT] [info] [infer_model.cpp:417] [configure] Configuring network group 'yolov11n' with params: batch size: 8, power mode: ULTRA_PERFORMANCE, latency: NONE
[2025-07-26 13:13:34.127] [34535] [HailoRT] [info] [multi_io_elements.cpp:754] [create] Created (AsyncHwEl)
[2025-07-26 13:13:34.128] [34535] [HailoRT] [info] [queue_elements.cpp:450] [create] Created (EntryPushQEl0yolov11n/input_layer1 | timeout: 10s)
[2025-07-26 13:13:34.128] [34535] [HailoRT] [info] [filter_elements.cpp:101] [create] Created (PreInferEl1yolov11n/input_layer1 | Reorder - src_order: NHWC, src_shape: (640, 640, 3), dst_order: NHCW, dst_shape: (640, 640, 3))
[2025-07-26 13:13:34.128] [34535] [HailoRT] [info] [queue_elements.cpp:450] [create] Created (PushQEl1yolov11n/input_layer1 | timeout: 10s)
[2025-07-26 13:13:34.129] [34535] [HailoRT] [info] [multi_io_elements.cpp:135] [create] Created (NmsPPMuxEl0YOLOV8-Post-Process | Op YOLOV8, Name: YOLOV8-Post-Process, Score threshold: 0.300, IoU threshold: 0.60, Classes: 9, Max bboxes per class: 100, Image height: 640, Image width: 640)
[2025-07-26 13:13:34.163] [34535] [HailoRT] [info] [queue_elements.cpp:942] [create] Created (MultiPushQEl0YOLOV8-Post-Process | timeout: 10s)
[2025-07-26 13:13:34.163] [34535] [HailoRT] [info] [edge_elements.cpp:187] [create] Created (LastAsyncEl0NmsPPMuxEl0YOLOV8-Post-Process)
[2025-07-26 13:13:34.163] [34535] [HailoRT] [info] [pipeline.cpp:891] [print_deep_description] EntryPushQEl0yolov11n/input_layer1 | inputs: user | outputs: PreInferEl1yolov11n/input_layer1(running in thread_id: 34585)
[2025-07-26 13:13:34.163] [34535] [HailoRT] [info] [pipeline.cpp:891] [print_deep_description] PreInferEl1yolov11n/input_layer1 | inputs: EntryPushQEl0yolov11n/input_layer1[0] | outputs: PushQEl1yolov11n/input_layer1
[2025-07-26 13:13:34.163] [34535] [HailoRT] [info] [pipeline.cpp:891] [print_deep_description] PushQEl1yolov11n/input_layer1 | inputs: PreInferEl1yolov11n/input_layer1[0] | outputs: AsyncHwEl(running in thread_id: 34586)
[2025-07-26 13:13:34.163] [34535] [HailoRT] [info] [pipeline.cpp:891] [print_deep_description] AsyncHwEl | inputs: PushQEl1yolov11n/input_layer1[0] | outputs: MultiPushQEl0YOLOV8-Post-Process MultiPushQEl0YOLOV8-Post-Process MultiPushQEl0YOLOV8-Post-Process MultiPushQEl0YOLOV8-Post-Process MultiPushQEl0YOLOV8-Post-Process MultiPushQEl0YOLOV8-Post-Process
[2025-07-26 13:13:34.163] [34535] [HailoRT] [info] [pipeline.cpp:891] [print_deep_description] MultiPushQEl0YOLOV8-Post-Process | inputs: AsyncHwEl[0] AsyncHwEl[1] AsyncHwEl[2] AsyncHwEl[3] AsyncHwEl[4] AsyncHwEl[5] | outputs: NmsPPMuxEl0YOLOV8-Post-Process(running in thread_id: 34587)
[2025-07-26 13:13:34.163] [34535] [HailoRT] [info] [pipeline.cpp:891] [print_deep_description] NmsPPMuxEl0YOLOV8-Post-Process | inputs: MultiPushQEl0YOLOV8-Post-Process[0] | outputs: LastAsyncEl0NmsPPMuxEl0YOLOV8-Post-Process
[2025-07-26 13:13:34.163] [34535] [HailoRT] [info] [pipeline.cpp:891] [print_deep_description] LastAsyncEl0NmsPPMuxEl0YOLOV8-Post-Process | inputs: NmsPPMuxEl0YOLOV8-Post-Process[0] | outputs: user
[2025-07-26 13:13:34.163] [34535] [HailoRT] [info] [hef.cpp:1994] [get_network_group_and_network_name] No name was given. Addressing all networks of default network_group: yolov11n
[2025-07-26 13:13:34.163] [34535] [HailoRT] [info] [hef.cpp:1994] [get_network_group_and_network_name] No name was given. Addressing all networks of default network_group: yolov11n
[2025-07-26 16:19:24.947] [36543] [HailoRT] [info] [device.cpp:51] [Device] OS Version: Linux 6.12.20+rpt-rpi-2712 #1 SMP PREEMPT Debian 1:6.12.20-1+rpt1~bpo12+1 (2025-03-19) aarch64
[2025-07-26 16:19:24.947] [36543] [HailoRT] [info] [control.cpp:113] [control__parse_identify_results] firmware_version is: 4.21.0
[2025-07-26 16:19:24.947] [36543] [HailoRT] [info] [control.cpp:113] [control__parse_identify_results] firmware_version is: 4.21.0
[2025-07-26 16:19:25.006] [36543] [HailoRT] [info] [device.cpp:51] [Device] OS Version: Linux 6.12.20+rpt-rpi-2712 #1 SMP PREEMPT Debian 1:6.12.20-1+rpt1~bpo12+1 (2025-03-19) aarch64
[2025-07-26 16:19:25.007] [36543] [HailoRT] [info] [control.cpp:113] [control__parse_identify_results] firmware_version is: 4.21.0
[2025-07-26 16:19:25.007] [36543] [HailoRT] [info] [control.cpp:113] [control__parse_identify_results] firmware_version is: 4.21.0
[2025-07-26 16:19:25.119] [36551] [HailoRT] [info] [device.cpp:51] [Device] OS Version: Linux 6.12.20+rpt-rpi-2712 #1 SMP PREEMPT Debian 1:6.12.20-1+rpt1~bpo12+1 (2025-03-19) aarch64
[2025-07-26 16:19:25.119] [36551] [HailoRT] [info] [control.cpp:113] [control__parse_identify_results] firmware_version is: 4.21.0
[2025-07-26 16:19:25.119] [36551] [HailoRT] [info] [control.cpp:113] [control__parse_identify_results] firmware_version is: 4.21.0
[2025-07-26 16:19:25.159] [36551] [HailoRT] [info] [vdevice.cpp:535] [create] Creating vdevice with params: device_count: 1, scheduling_algorithm: ROUND_ROBIN, multi_process_service: true
[2025-07-26 16:19:25.174] [36551] [HailoRT] [info] [device.cpp:51] [Device] OS Version: Linux 6.12.20+rpt-rpi-2712 #1 SMP PREEMPT Debian 1:6.12.20-1+rpt1~bpo12+1 (2025-03-19) aarch64
[2025-07-26 16:19:25.174] [36551] [HailoRT] [info] [control.cpp:113] [control__parse_identify_results] firmware_version is: 4.21.0
[2025-07-26 16:19:25.180] [36551] [HailoRT] [info] [hef.cpp:1994] [get_network_group_and_network_name] No name was given. Addressing all networks of default network_group: yolov11n
[2025-07-26 16:19:25.180] [36551] [HailoRT] [info] [hef.cpp:1994] [get_network_group_and_network_name] No name was given. Addressing all networks of default network_group: yolov11n
[2025-07-26 16:19:25.311] [36551] [HailoRT] [info] [infer_model.cpp:417] [configure] Configuring network group 'yolov11n' with params: batch size: 8, power mode: ULTRA_PERFORMANCE, latency: NONE
[2025-07-26 16:19:25.312] [36551] [HailoRT] [info] [multi_io_elements.cpp:754] [create] Created (AsyncHwEl)
[2025-07-26 16:19:25.313] [36551] [HailoRT] [info] [queue_elements.cpp:450] [create] Created (EntryPushQEl0yolov11n/input_layer1 | timeout: 10s)
[2025-07-26 16:19:25.313] [36551] [HailoRT] [info] [filter_elements.cpp:101] [create] Created (PreInferEl1yolov11n/input_layer1 | Reorder - src_order: NHWC, src_shape: (640, 640, 3), dst_order: NHCW, dst_shape: (640, 640, 3))
[2025-07-26 16:19:25.313] [36551] [HailoRT] [info] [queue_elements.cpp:450] [create] Created (PushQEl1yolov11n/input_layer1 | timeout: 10s)
[2025-07-26 16:19:25.314] [36551] [HailoRT] [info] [multi_io_elements.cpp:135] [create] Created (NmsPPMuxEl0YOLOV8-Post-Process | Op YOLOV8, Name: YOLOV8-Post-Process, Score threshold: 0.300, IoU threshold: 0.60, Classes: 9, Max bboxes per class: 100, Image height: 640, Image width: 640)
[2025-07-26 16:19:25.331] [36551] [HailoRT] [info] [queue_elements.cpp:942] [create] Created (MultiPushQEl0YOLOV8-Post-Process | timeout: 10s)
[2025-07-26 16:19:25.331] [36551] [HailoRT] [info] [edge_elements.cpp:187] [create] Created (LastAsyncEl0NmsPPMuxEl0YOLOV8-Post-Process)
[2025-07-26 16:19:25.331] [36551] [HailoRT] [info] [pipeline.cpp:891] [print_deep_description] EntryPushQEl0yolov11n/input_layer1 | inputs: user | outputs: PreInferEl1yolov11n/input_layer1(running in thread_id: 36598)
[2025-07-26 16:19:25.331] [36551] [HailoRT] [info] [pipeline.cpp:891] [print_deep_description] PreInferEl1yolov11n/input_layer1 | inputs: EntryPushQEl0yolov11n/input_layer1[0] | outputs: PushQEl1yolov11n/input_layer1
[2025-07-26 16:19:25.331] [36551] [HailoRT] [info] [pipeline.cpp:891] [print_deep_description] PushQEl1yolov11n/input_layer1 | inputs: PreInferEl1yolov11n/input_layer1[0] | outputs: AsyncHwEl(running in thread_id: 36599)
[2025-07-26 16:19:25.331] [36551] [HailoRT] [info] [pipeline.cpp:891] [print_deep_description] AsyncHwEl | inputs: PushQEl1yolov11n/input_layer1[0] | outputs: MultiPushQEl0YOLOV8-Post-Process MultiPushQEl0YOLOV8-Post-Process MultiPushQEl0YOLOV8-Post-Process MultiPushQEl0YOLOV8-Post-Process MultiPushQEl0YOLOV8-Post-Process MultiPushQEl0YOLOV8-Post-Process
[2025-07-26 16:19:25.331] [36551] [HailoRT] [info] [pipeline.cpp:891] [print_deep_description] MultiPushQEl0YOLOV8-Post-Process | inputs: AsyncHwEl[0] AsyncHwEl[1] AsyncHwEl[2] AsyncHwEl[3] AsyncHwEl[4] AsyncHwEl[5] | outputs: NmsPPMuxEl0YOLOV8-Post-Process(running in thread_id: 36600)
[2025-07-26 16:19:25.331] [36551] [HailoRT] [info] [pipeline.cpp:891] [print_deep_description] NmsPPMuxEl0YOLOV8-Post-Process | inputs: MultiPushQEl0YOLOV8-Post-Process[0] | outputs: LastAsyncEl0NmsPPMuxEl0YOLOV8-Post-Process
[2025-07-26 16:19:25.331] [36551] [HailoRT] [info] [pipeline.cpp:891] [print_deep_description] LastAsyncEl0NmsPPMuxEl0YOLOV8-Post-Process | inputs: NmsPPMuxEl0YOLOV8-Post-Process[0] | outputs: user
[2025-07-26 16:19:25.331] [36551] [HailoRT] [info] [hef.cpp:1994] [get_network_group_and_network_name] No name was given. Addressing all networks of default network_group: yolov11n
[2025-07-26 16:19:25.331] [36551] [HailoRT] [info] [hef.cpp:1994] [get_network_group_and_network_name] No name was given. Addressing all networks of default network_group: yolov11n
