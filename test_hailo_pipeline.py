#!/usr/bin/env python3
"""
Test script for the HailoPipeline class.

This script demonstrates how to use the HailoPipeline class as an alternative
to the PyTorch model interface for Hailo inference.
"""

import cv2
import numpy as np
from src.fridge.hailo_pipeline import HailoPipeline, HAILO_AVAILABLE


def test_hailo_pipeline():
    """Test the HailoPipeline class with a sample image."""
    
    if not HAILO_AVAILABLE:
        print("Hailo dependencies not available. Cannot run test.")
        return
    
    # Path to your HEF model
    hef_path = "models/hailo/long_aug/long_aug.hef"
    
    try:
        # Initialize the pipeline
        print("Initializing HailoPipeline...")
        pipeline = HailoPipeline(hef_path)
        
        # Create a test image (640x640 RGB)
        test_image = np.random.randint(0, 255, (640, 640, 3), dtype=np.uint8)
        
        # Or load a real image if available
        # test_image = cv2.imread("test_image.jpg")
        # test_image = cv2.cvtColor(test_image, cv2.COLOR_BGR2RGB)
        
        print("Running inference...")
        confidences, bounding_boxes = pipeline.predict(test_image)
        
        print(f"Confidences shape: {confidences.shape}")
        print(f"Confidences: {confidences}")
        print(f"Number of detections: {len(bounding_boxes)}")
        print(f"Bounding boxes: {bounding_boxes}")
        
        # Clean up
        pipeline._stop_pipeline()
        print("Test completed successfully!")
        
    except Exception as e:
        print(f"Error during test: {e}")
        import traceback
        traceback.print_exc()


def test_model_integration():
    """Test the Model class with HailoPipeline integration."""
    
    # First, you need to enable HailoPipeline in model.py by setting:
    # def use_hailo_pipeline() -> bool:
    #     return True  # Change this to True
    
    from src.fridge.model import Model, use_hailo_pipeline
    
    if not use_hailo_pipeline():
        print("HailoPipeline not enabled in model.py. Set use_hailo_pipeline() to return True.")
        return
    
    try:
        print("Testing Model class with HailoPipeline...")
        model = Model()
        
        # Create test frame
        test_frame = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
        
        # Run prediction
        confidences, bounding_boxes = model.predict(test_frame)
        
        print(f"Model prediction - Confidences: {confidences}")
        print(f"Model prediction - Bounding boxes: {bounding_boxes}")
        
        print("Model integration test completed!")
        
    except Exception as e:
        print(f"Error during model integration test: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    print("=== HailoPipeline Test ===")
    test_hailo_pipeline()
    
    print("\n=== Model Integration Test ===")
    test_model_integration()
